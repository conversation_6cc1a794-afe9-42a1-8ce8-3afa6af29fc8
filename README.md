# Smart Form Filler Browser Extension

## الفكرة الأساسية
إضافة للمتصفح تسمح للمستخدمين بتعبئة النماذج عبر الإنترنت بشكل تلقائي باستخدام بيانات مخزنة مسبقاً، مع إمكانية تخصيص عملية التعبئة بشكل كامل.

## ✅ التحديثات الجديدة
- **تكامل رفع الملفات**: تم تعديل popup.html لدعم رفع الملفات والانتقال التلقائي إلى split-view
- **بنية الإضافة الكاملة**: تم إضافة جميع الملفات اللازمة لإضافة متصفح عاملة
- **دعم الواجهة العربية**: دعم كامل للغة العربية مع تخطيط RTL
- **وضع التعلم الذكي**: إمكانيات كشف وتعيين الحقول التلقائية

## الأهداف
1. توفير الوقت والجهد في تعبئة النماذج المتكررة
2. تقليل الأخطاء في إدخال البيانات
3. تمكين المستخدمين من التحكم الكامل في عملية التعبئة
4. دعم مختلف المنصات بما فيها يوتيوب

## كيفية العمل

### 1. إعداد البيانات
- يقوم المستخدم برفع ملف بيانات (CSV أو JSON) يحتوي على المعلومات المراد استخدامها
- يمكن للمستخدم تنظيم البيانات في جداول وتسمية الأعمدة بأسماء ذات معنى

### 2. وضع علامات التعبئة (التخصيص)
- يدخل المستخدم وضع "التعليم" من خلال الإضافة
- ينقر على حقل في النموذج ويربطه بعمود محدد من جدول البيانات
- يمكنه أيضاً تحديد أزرار التقديم أو أي عناصر تفاعلية أخرى
- يحفظ هذا التخصيص كـ "نمط" يمكن استخدامه لاحقاً

### 3. التعبئة التلقائية
- عند زيارة صفحة تم إعداد نمط لها، تظهر أيقونة تنبيه
- يمكن للمستخدم اختيار صف البيانات المراد استخدامه
- بنقرة واحدة، تقوم الإضافة بتعبئة جميع الحقول المحددة مسبقاً

### 4. ميزات متقدمة
- حفظ أنماط متعددة لمواقع مختلفة
- تشغيل تلقائي عند زيارة مواقع محددة
- دعم للتعبئة المتسلسلة (تعبئة عدة صفحات متتالية)
- إمكانية مشاركة الأنماط مع مستخدمين آخرين

## 🚀 كيفية الاستخدام الجديد

### الخطوة 1: رفع البيانات والانتقال إلى واجهة التعبئة
1. انقر على أيقونة الإضافة في شريط الأدوات
2. **طريقتان لرفع الملف:**
   - انقر على "اختر ملف" واختر ملف CSV أو JSON
   - اسحب وأفلت الملف مباشرة على منطقة الرفع
3. تأكد من ظهور معلومات الملف (الاسم والحجم)
4. **جديد:** انقر على "🚀 انتقل إلى واجهة التعبئة"
5. ستفتح نافذة جديدة تحتوي على واجهة التعبئة المقسمة

### الخطوة 2: إعداد الموقع والبيانات
1. في الجانب الأيمن: ستظهر بياناتك في جدول تفاعلي
2. في الجانب الأيسر: أدخل رابط الموقع المراد تعبئة النموذج فيه
3. انقر "تحميل" لفتح الموقع في الإطار
4. حدد الصفوف التي تريد استخدامها للتعبئة

### الخطوة 2: تعليم الإضافة
1. اذهب إلى الصفحة التي تريد ملء النموذج فيها
2. فعل "وضع التعليم" من الإضافة
3. **ستلاحظ:** ظهور إطار أزرق متقطع حول العناصر القابلة للتفاعل
4. انقر بالزر الأيمن على كل حقل في النموذج
5. **يجب أن تظهر:** قائمة بأعمدة البيانات المرفوعة
6. اختر العمود المناسب من القائمة
7. حدد زر الإرسال بنفس الطريقة

### الخطوة 3: تشغيل التعبئة التلقائية
1. أوقف وضع التعليم
2. انقر على "بدء التعبئة التلقائية"
3. راقب الإضافة وهي تملأ النماذج تلقائياً

## 🧪 اختبار الإضافة
استخدم ملف `test-teaching-mode.html` لاختبار جميع وظائف الإضافة:
1. افتح الملف في المتصفح
2. ارفع ملف البيانات في الإضافة
3. فعل وضع التعليم
4. اختبر النقر بالزر الأيمن على العناصر المختلفة

## 📁 بنية الملفات الجديدة
```
├── manifest.json          # ملف إعدادات الإضافة (Manifest V3)
├── popup.html             # الواجهة الرئيسية لرفع الملفات
├── popup.js               # وظائف رفع الملفات والانتقال
├── split-view.html        # واجهة التعبئة المقسمة
├── split-view.js          # وظائف واجهة التعبئة
├── content.js             # سكريبت التفاعل مع النماذج
├── background.js          # خدمة العمل في الخلفية
├── utils.js               # وظائف مساعدة مشتركة
├── styles.css             # ملف الأنماط الرئيسي
├── content-styles.css     # أنماط سكريبت المحتوى
└── icons/                 # أيقونات الإضافة
```

## 🔧 استكشاف الأخطاء وإصلاحها
إذا واجهت مشاكل، راجع ملف `TROUBLESHOOTING.md` للحلول المفصلة.

## 🛠️ التثبيت والتطوير
1. حمل أو استنسخ هذا المستودع
2. افتح Chrome/Edge واذهب إلى `chrome://extensions/`
3. فعل "وضع المطور" (Developer mode)
4. انقر "تحميل غير مضغوط" (Load unpacked) واختر مجلد الإضافة
5. ستظهر أيقونة الإضافة في شريط الأدوات

## الفئة المستهدفة
هذه الإضافة مثالية للأشخاص الذين يتعاملون مع نماذج متكررة، مثل:
- مديري المحتوى
- مسؤولي التسويق
- محترفي إدخال البيانات
- أي شخص يحتاج لإدخال بيانات متشابهة بشكل متكرر