# Smart Form Filler

## الفكرة الأساسية
إضافة للمتصفح تسمح للمستخدمين بتعبئة النماذج عبر الإنترنت بشكل تلقائي باستخدام بيانات مخزنة مسبقاً، مع إمكانية تخصيص عملية التعبئة بشكل كامل.

## الأهداف
1. توفير الوقت والجهد في تعبئة النماذج المتكررة
2. تقليل الأخطاء في إدخال البيانات
3. تمكين المستخدمين من التحكم الكامل في عملية التعبئة
4. دعم مختلف المنصات بما فيها يوتيوب

## كيفية العمل

### 1. إعداد البيانات
- يقوم المستخدم برفع ملف بيانات (CSV أو JSON) يحتوي على المعلومات المراد استخدامها
- يمكن للمستخدم تنظيم البيانات في جداول وتسمية الأعمدة بأسماء ذات معنى

### 2. وضع علامات التعبئة (التخصيص)
- يدخل المستخدم وضع "التعليم" من خلال الإضافة
- ينقر على حقل في النموذج ويربطه بعمود محدد من جدول البيانات
- يمكنه أيضاً تحديد أزرار التقديم أو أي عناصر تفاعلية أخرى
- يحفظ هذا التخصيص كـ "نمط" يمكن استخدامه لاحقاً

### 3. التعبئة التلقائية
- عند زيارة صفحة تم إعداد نمط لها، تظهر أيقونة تنبيه
- يمكن للمستخدم اختيار صف البيانات المراد استخدامه
- بنقرة واحدة، تقوم الإضافة بتعبئة جميع الحقول المحددة مسبقاً

### 4. ميزات متقدمة
- حفظ أنماط متعددة لمواقع مختلفة
- تشغيل تلقائي عند زيارة مواقع محددة
- دعم للتعبئة المتسلسلة (تعبئة عدة صفحات متتالية)
- إمكانية مشاركة الأنماط مع مستخدمين آخرين

## 🚀 كيفية الاستخدام

### الخطوة 1: رفع البيانات
1. انقر على أيقونة الإضافة في شريط الأدوات
2. اختر "رفع ملف البيانات"
3. حدد ملف CSV أو JSON يحتوي على بياناتك
4. تأكد من ظهور معاينة البيانات
5. **مهم:** تأكد من ظهور رسالة "تم رفع البيانات بنجاح"

### الخطوة 2: تعليم الإضافة
1. اذهب إلى الصفحة التي تريد ملء النموذج فيها
2. فعل "وضع التعليم" من الإضافة
3. **ستلاحظ:** ظهور إطار أزرق متقطع حول العناصر القابلة للتفاعل
4. انقر بالزر الأيمن على كل حقل في النموذج
5. **يجب أن تظهر:** قائمة بأعمدة البيانات المرفوعة
6. اختر العمود المناسب من القائمة
7. حدد زر الإرسال بنفس الطريقة

### الخطوة 3: تشغيل التعبئة التلقائية
1. أوقف وضع التعليم
2. انقر على "بدء التعبئة التلقائية"
3. راقب الإضافة وهي تملأ النماذج تلقائياً

## 🧪 اختبار الإضافة
استخدم ملف `test-teaching-mode.html` لاختبار جميع وظائف الإضافة:
1. افتح الملف في المتصفح
2. ارفع ملف البيانات في الإضافة
3. فعل وضع التعليم
4. اختبر النقر بالزر الأيمن على العناصر المختلفة

## 🔧 استكشاف الأخطاء وإصلاحها
إذا واجهت مشاكل، راجع ملف `TROUBLESHOOTING.md` للحلول المفصلة.

## الفئة المستهدفة
هذه الإضافة مثالية للأشخاص الذين يتعاملون مع نماذج متكررة، مثل:
- مديري المحتوى
- مسؤولي التسويق
- محترفي إدخال البيانات
- أي شخص يحتاج لإدخال بيانات متشابهة بشكل متكرر