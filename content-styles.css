/* Smart Form Filler - Content Styles */

/* Teaching Mode Styles */
.smart-form-highlightable {
    outline: 2px dashed #4facfe !important;
    outline-offset: 2px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.smart-form-highlightable:hover {
    outline-color: #00f2fe !important;
    background-color: rgba(79, 172, 254, 0.1) !important;
    transform: scale(1.02) !important;
}

.smart-form-mapped {
    outline: 2px solid #28a745 !important;
    outline-offset: 2px !important;
    background-color: rgba(40, 167, 69, 0.05) !important;
}

.smart-form-mapped::after {
    content: "✓";
    position: absolute;
    top: -8px;
    right: -8px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    z-index: 1000;
}

/* Context Menu Styles */
#smart-form-context-menu {
    position: fixed;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 10001;
    min-width: 220px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    direction: rtl;
    overflow: hidden;
    animation: smart-form-menu-appear 0.2s ease-out;
}

@keyframes smart-form-menu-appear {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

#smart-form-context-menu .menu-header {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    font-weight: 600;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    color: #333;
}

#smart-form-context-menu .menu-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

#smart-form-context-menu .menu-item:last-child {
    border-bottom: none;
}

#smart-form-context-menu .menu-item:hover {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    transform: translateX(-2px);
}

#smart-form-context-menu .menu-item.danger {
    color: #dc3545;
}

#smart-form-context-menu .menu-item.danger:hover {
    background: #dc3545;
    color: white;
}

/* Notification Styles */
.smart-form-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 8px;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 350px;
    direction: rtl;
    animation: smart-form-notification-slide 0.3s ease-out;
    backdrop-filter: blur(10px);
}

@keyframes smart-form-notification-slide {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.smart-form-notification.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.smart-form-notification.error {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
}

.smart-form-notification.info {
    background: linear-gradient(135deg, #17a2b8 0%, #4facfe 100%);
}

.smart-form-notification.warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    color: #333;
}

/* Pattern Notification Styles */
#smart-form-pattern-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 320px;
    direction: rtl;
    animation: smart-form-pattern-notification-appear 0.4s ease-out;
    backdrop-filter: blur(10px);
}

@keyframes smart-form-pattern-notification-appear {
    from {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

#smart-form-pattern-notification .notification-header {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

#smart-form-pattern-notification .notification-content {
    margin-bottom: 15px;
    opacity: 0.9;
    line-height: 1.4;
}

#smart-form-pattern-notification .notification-actions {
    display: flex;
    gap: 10px;
}

#smart-form-pattern-notification button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

#smart-form-pattern-notification .btn-primary {
    background: white;
    color: #4facfe;
}

#smart-form-pattern-notification .btn-primary:hover {
    background: #f8f9ff;
    transform: translateY(-1px);
}

#smart-form-pattern-notification .btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

#smart-form-pattern-notification .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Progress Indicator */
.smart-form-progress {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 10002;
    text-align: center;
    min-width: 300px;
    direction: rtl;
    animation: smart-form-progress-appear 0.3s ease-out;
}

@keyframes smart-form-progress-appear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.smart-form-progress .progress-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: smart-form-spin 1s linear infinite;
}

@keyframes smart-form-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.smart-form-progress .progress-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.smart-form-progress .progress-bar {
    width: 100%;
    height: 6px;
    background: #eee;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 15px;
}

.smart-form-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* Overlay */
.smart-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    backdrop-filter: blur(2px);
    animation: smart-form-overlay-appear 0.2s ease-out;
}

@keyframes smart-form-overlay-appear {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Field Highlighting Effects */
.smart-form-field-filling {
    animation: smart-form-field-fill 0.5s ease-in-out;
}

@keyframes smart-form-field-fill {
    0% {
        background-color: rgba(79, 172, 254, 0.1);
        transform: scale(1);
    }
    50% {
        background-color: rgba(79, 172, 254, 0.3);
        transform: scale(1.02);
    }
    100% {
        background-color: rgba(40, 167, 69, 0.1);
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .smart-form-notification,
    #smart-form-pattern-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    #smart-form-context-menu {
        min-width: 200px;
    }
    
    .smart-form-progress {
        right: 20px;
        left: 20px;
        min-width: auto;
        transform: translate(0, -50%);
        position: fixed;
        top: 50%;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #smart-form-context-menu {
        background: #2d3748;
        border-color: #4a5568;
        color: white;
    }
    
    #smart-form-context-menu .menu-header {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        color: white;
    }
    
    #smart-form-context-menu .menu-item {
        border-color: #4a5568;
    }
    
    .smart-form-progress {
        background: #2d3748;
        color: white;
    }
    
    .smart-form-progress .progress-text {
        color: white;
    }
}

/* Accessibility */
.smart-form-highlightable:focus {
    outline: 3px solid #4facfe !important;
    outline-offset: 2px !important;
}

@media (prefers-reduced-motion: reduce) {
    .smart-form-highlightable,
    .smart-form-notification,
    #smart-form-pattern-notification,
    .smart-form-progress,
    .smart-form-overlay {
        animation: none !important;
        transition: none !important;
    }
    
    .smart-form-highlightable:hover {
        transform: none !important;
    }
}
