<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Form Filler</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Smart Form Filler</h1>
            <p>أداة ذكية لتعبئة النماذج تلقائياً</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- File Upload Section -->
            <div class="section" id="upload-section">
                <h2>📁 رفع ملف البيانات</h2>
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">📄</div>
                    <p>اسحب وأفلت ملف البيانات هنا</p>
                    <p class="file-types"><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Excel مدعوم</p>
                    <input type="file" id="file-input" accept=".csv,.json,.xlsx,.xls" hidden>
                    <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                        اختر ملف
                    </button>
                </div>
                <div class="file-info" id="file-info" style="display: none;">
                    <div class="file-details">
                        <span class="file-name" id="file-name"></span>
                        <span class="file-size" id="file-size"></span>
                    </div>
                    <button class="btn btn-secondary btn-small" id="remove-file">إزالة</button>
                </div>

                <!-- Navigation Section -->
                <div class="navigation-section" id="navigation-section" style="display: none;">
                    <h3>✅ تم تحميل الملف بنجاح</h3>
                    <p>يمكنك الآن الانتقال إلى واجهة التعبئة التلقائية</p>
                    <button class="btn btn-success btn-large" id="go-to-split-view">
                        🚀 انتقل إلى واجهة التعبئة
                    </button>
                </div>
            </div>

            

                <!-- Footer -->
        <div class="footer">
            <div class="status" id="status">جاهز</div>
            <div class="version">الإصدار 1.0.0</div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="utils.js"></script>
    <script src="popup.js"></script>
</body>
</html>
