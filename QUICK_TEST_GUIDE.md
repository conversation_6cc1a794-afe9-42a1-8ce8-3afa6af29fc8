# دليل الاختبار السريع - تحليل هيكل الصفحة

## 🚀 خطوات الاختبار السريع

### الخطوة 1: تحضير البيانات
1. افتح `popup.html` في المتصفح
2. ارفع أي ملف CSV أو JSON (يمكنك إنشاء ملف بسيط للاختبار)
3. انقر "🚀 انتقل إلى واجهة التعبئة"

### الخطوة 2: فتح صفحة الاختبار
1. **افتح `test-page-analysis.html` في المتصفح أولاً**
2. **انقر زر "📋 نسخ المسار"** لنسخ المسار الكامل
3. **في split-view، الصق المسار في حقل URL**
4. **انقر "تحميل"** لفتح الصفحة في iframe

**مثال على المسار:**
```
file:///C:/Users/<USER>/Desktop/اضافة/test-page-analysis.html
```

### الخطوة 3: تشغيل تحليل الهيكل
1. **انقر على زر "🔍 كشف الحقول"** (هذا هو الزر المضاف حديثاً)
2. راقب ظهور إشعار "تم تحليل هيكل الصفحة وكشف الحقول"
3. ستظهر لوحة تحليل جديدة أسفل جدول البيانات

### الخطوة 4: استكشاف النتائج
1. **لوحة التحليل**: ستظهر إحصائيات سريعة وتفاصيل مفصلة
2. **التمييز البصري**: العناصر في الصفحة ستكون مميزة بألوان:
   - 🔵 النماذج: إطار أزرق
   - 🟢 حقول الإدخال: إطار أخضر
   - 🟣 القوائم المنسدلة: إطار بنفسجي
   - 🟡 الأزرار: إطار أصفر

## 📋 ملف CSV للاختبار
إنشئ ملف `test-data.csv` بالمحتوى التالي:
```csv
الاسم,البريد الإلكتروني,الهاتف,العمر,الجنس,المدينة
أحمد محمد,<EMAIL>,0501234567,25,ذكر,الرياض
فاطمة علي,<EMAIL>,0509876543,30,أنثى,جدة
محمد سالم,<EMAIL>,0507654321,28,ذكر,الدمام
```

## 🔧 استكشاف الأخطاء

### ❌ خطأ CORS (الأكثر شيوعاً):
```
خطأ في تحليل هيكل الصفحة: Failed to read a named property 'document' from 'Window'
```

**الحل:**
1. ✅ **استخدم الملفات المحلية** (`test-page-analysis.html`)
2. ✅ **تأكد من المسار الصحيح** يبدأ بـ `file://`
3. ✅ **النظام سيحاول طرق بديلة** تلقائياً للمواقع الخارجية

### إذا لم يظهر زر "🔍 كشف الحقول":
1. تأكد من تحديث الصفحة
2. تحقق من وحدة تحكم المطور للأخطاء

### إذا لم يعمل التحليل مع الملفات المحلية:
1. تأكد من المسار الكامل للملف
2. تحقق من أن الملف موجود في المكان الصحيح
3. استخدم زر "نسخ المسار" من صفحة الاختبار

### إذا لم تظهر لوحة التحليل:
1. تحقق من وجود أخطاء في وحدة التحكم
2. تأكد من وجود نماذج في الصفحة المحملة
3. راقب رسائل النظام لفهم نوع التحليل المستخدم

## ✅ النتائج المتوقعة

عند استخدام `test-page-analysis.html`:
- **النماذج**: 2 نماذج مكتشفة
- **حقول الإدخال**: حوالي 10 حقول
- **القوائم المنسدلة**: 2 قوائم
- **الأزرار**: حوالي 8 أزرار
- **الروابط**: 7 روابط

## 🎯 الميزات المتاحة الآن

✅ **تم تنفيذها:**
- زر "🔍 كشف الحقول" يعمل
- تحليل شامل لهيكل الصفحة
- لوحة تحليل تفاعلية
- تمييز بصري للعناصر
- إشعارات النجاح والأخطاء

🔄 **قيد التطوير:**
- ربط البيانات بالحقول
- التعبئة التلقائية
- حفظ أنماط التعبئة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من وحدة تحكم المطور (F12)
2. تأكد من تحميل جميع الملفات بشكل صحيح
3. جرب إعادة تحميل الصفحة

---

**ملاحظة**: هذه الميزة تعمل بشكل أفضل مع المواقع التي تسمح بالتضمين في iframe. بعض المواقع قد تمنع ذلك لأسباب أمنية.
