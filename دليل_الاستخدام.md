# دليل استخدام Smart Form Filler

## 🚀 كيفية استخدام النظام المحسن

### الخطوة 1: تحضير البيانات
1. افتح ملف `sample-data.csv` الموجود في المجلد
2. أو ارفع ملف CSV خاص بك يحتوي على البيانات المطلوبة

### الخطوة 2: فتح الإضافة
1. انقر على أيقونة الإضافة في المتصفح
2. ارفع ملف البيانات (CSV)
3. انقر على "فتح العرض المقسم"

### الخطوة 3: تحميل النموذج
1. في العرض المقسم، أدخل رابط النموذج في حقل URL
   - للاختبار: استخدم `file:///[مسار_المجلد]/test-form.html`
   - أو أي رابط نموذج آخر
2. ان<PERSON><PERSON> على "تحميل"

### الخطوة 4: كشف الحقول
1. انقر على زر "🔍 كشف الحقول"
2. سيتم تمييز جميع الحقول القابلة للتعبئة باللون الأزرق

### الخطوة 5: ربط الحقول (التعيين التلقائي)
1. انقر على زر "⚙️ تعيين تلقائي"
2. ستظهر نافذة تحتوي على جميع الحقول المكتشفة
3. لكل حقل، اختر العمود المناسب من البيانات
4. أو أدخل قيمة ثابتة بالنقر على "مخصص"
5. انقر على "💾 حفظ الإعدادات"

### الخطوة 6: تحديد البيانات للتعبئة
1. في الجانب الأيمن، حدد الصفوف التي تريد تعبئتها
2. يمكنك تحديد صف واحد أو عدة صفوف
3. أو انقر على "تحديد الكل" لتحديد جميع الصفوف

### الخطوة 7: بدء التعبئة
1. انقر على زر "🚀 تعبئة"
2. سيبدأ النظام في تعبئة النموذج تلقائياً
3. ستظهر رسائل تأكيد لكل خطوة
4. الصفوف المكتملة ستظهر باللون الأخضر مع علامة ✅

## 🎯 ميزات النظام المحسن

### كشف الحقول الذكي
- يكتشف جميع أنواع الحقول: نص، أرقام، قوائم منسدلة، مناطق نص
- يستخرج أسماء الحقول من labels تلقائياً
- يدعم الحقول المطلوبة والاختيارية

### ربط البيانات المرن
- ربط الأعمدة بالحقول
- قيم ثابتة مخصصة
- معاينة البيانات قبل الربط
- حفظ الإعدادات للاستخدام المستقبلي

### تعبئة ذكية
- محاكاة الكتابة الطبيعية
- دعم القوائم المنسدلة
- دعم أزرار الراديو والمربعات
- تأخير مناسب بين الحقول

### تتبع التقدم
- عرض الصفوف المكتملة
- رسائل تأكيد لكل خطوة
- إحصائيات مفصلة
- تمييز بصري للحالة

## 🔧 استكشاف الأخطاء

### مشكلة: لا يتم كشف الحقول
**الحل:**
- تأكد من تحميل الصفحة بالكامل
- جرب تحديث الصفحة
- تحقق من أن النموذج يحتوي على حقول مرئية

### مشكلة: لا تعمل التعبئة
**الحل:**
- تأكد من ربط الحقول أولاً
- تحقق من تحديد الصفوف
- تأكد من صحة البيانات

### مشكلة: خطأ في الوصول للصفحة
**الحل:**
- تأكد من صحة الرابط
- للملفات المحلية، استخدم `file:///` في بداية المسار
- تحقق من إعدادات الأمان في المتصفح

## 📝 نصائح للاستخدام الأمثل

1. **تحضير البيانات:**
   - استخدم أسماء أعمدة واضحة ومفهومة
   - تأكد من عدم وجود خلايا فارغة في الصفوف المهمة
   - احفظ الملف بترميز UTF-8 للنصوص العربية

2. **ربط الحقول:**
   - ابدأ بالحقول المطلوبة أولاً
   - استخدم القيم الثابتة للحقول التي لا تتغير
   - جرب الربط التلقائي أولاً ثم عدل حسب الحاجة

3. **التعبئة:**
   - ابدأ بصف واحد للاختبار
   - راقب الرسائل والتأكيدات
   - احفظ الإعدادات للاستخدام المستقبلي

## 🎉 مثال عملي

1. افتح `test-form.html` في المتصفح
2. ارفع `sample-data.csv` في الإضافة
3. اكشف الحقول وربطها:
   - `first_name` → الاسم الأول
   - `last_name` → الاسم الأخير  
   - `email` → البريد الإلكتروني
   - `phone` → رقم الهاتف
   - `country` → البلد
   - وهكذا...
4. حدد صف واحد وجرب التعبئة
5. إذا نجح، حدد المزيد من الصفوف

---

**ملاحظة:** هذا النظام المحسن يركز على البساطة والفعالية. جميع الميزات المعقدة تم تبسيطها لضمان عمل النظام بشكل موثوق.
