// Smart Form Filler - Content Script

// Global variables
let isSmartLearningActive = false;
let detectedFields = [];
let fieldMappings = new Map();
let highlightedElements = new Set();

// Initialize content script
(function() {
    console.log('Smart Form Filler content script loaded');
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();

function init() {
    // Setup message listener
    chrome.runtime.onMessage.addListener(handleMessage);
    
    // Auto-detect forms on page load
    detectFormsOnPage();
    
    // Setup mutation observer to detect dynamic forms
    setupMutationObserver();
    
    console.log('Smart Form Filler initialized on:', window.location.href);
}

// Handle messages from extension
function handleMessage(request, _sender, sendResponse) {
    console.log('Content script received message:', request);
    
    switch (request.action) {
        case 'detectFields':
            sendResponse({ success: true, fields: detectFormFields() });
            break;
            
        case 'highlightFields':
            highlightFormFields(request.fields);
            sendResponse({ success: true });
            break;
            
        case 'fillForm':
            fillFormWithData(request.data);
            sendResponse({ success: true });
            break;
            
        case 'startSmartLearning':
            startSmartLearning();
            sendResponse({ success: true });
            break;
            
        case 'stopSmartLearning':
            stopSmartLearning();
            sendResponse({ success: true });
            break;
            
        case 'getPageInfo':
            sendResponse({
                success: true,
                info: {
                    url: window.location.href,
                    title: document.title,
                    forms: document.querySelectorAll('form').length,
                    fields: document.querySelectorAll('input, select, textarea').length
                }
            });
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
    
    return true;
}

// Detect all forms and fields on the page
function detectFormsOnPage() {
    const forms = document.querySelectorAll('form');
    console.log(`Found ${forms.length} forms on page`);
    
    forms.forEach((form, index) => {
        const fields = form.querySelectorAll('input, select, textarea');
        console.log(`Form ${index + 1} has ${fields.length} fields`);
    });
}

// Detect form fields and return structured data
function detectFormFields() {
    const fields = [];
    const forms = document.querySelectorAll('form');
    
    forms.forEach((form, formIndex) => {
        const formFields = form.querySelectorAll('input, select, textarea');
        
        formFields.forEach((field, fieldIndex) => {
            const fieldInfo = {
                formIndex: formIndex,
                fieldIndex: fieldIndex,
                type: field.type || field.tagName.toLowerCase(),
                name: field.name || '',
                id: field.id || '',
                placeholder: field.placeholder || '',
                label: getFieldLabel(field),
                required: field.required,
                value: field.value || '',
                selector: generateUniqueSelector(field),
                boundingRect: field.getBoundingClientRect()
            };
            
            // Special handling for select elements
            if (field.tagName.toLowerCase() === 'select') {
                fieldInfo.options = Array.from(field.options).map(option => ({
                    value: option.value,
                    text: option.text
                }));
            }
            
            fields.push(fieldInfo);
        });
    });
    
    detectedFields = fields;
    return fields;
}

// Highlight form fields on the page
function highlightFormFields(fields) {
    // Remove existing highlights
    removeAllHighlights();
    
    fields.forEach(field => {
        const element = document.querySelector(field.selector);
        if (element) {
            element.classList.add('field-highlight');
            highlightedElements.add(element);
        }
    });
}

// Remove all field highlights
function removeAllHighlights() {
    highlightedElements.forEach(element => {
        element.classList.remove('field-highlight', 'field-mapped', 'field-filling');
    });
    highlightedElements.clear();
}

// Fill form with provided data
function fillFormWithData(data) {
    console.log('Filling form with data:', data);
    
    data.mappings.forEach(mapping => {
        const element = document.querySelector(mapping.selector);
        if (element) {
            // Add filling highlight
            element.classList.add('field-filling');
            
            setTimeout(() => {
                try {
                    if (element.tagName.toLowerCase() === 'select') {
                        fillSelectField(element, mapping.value);
                    } else {
                        fillInputField(element, mapping.value);
                    }
                    
                    // Change to mapped highlight
                    element.classList.remove('field-filling');
                    element.classList.add('field-mapped');
                    
                } catch (error) {
                    console.error('Error filling field:', error);
                    element.classList.remove('field-filling');
                }
            }, mapping.delay || 100);
        }
    });
}

// Fill input field
function fillInputField(element, value) {
    // Clear existing value
    element.value = '';
    
    // Simulate typing for better compatibility
    element.focus();
    
    // Set value
    element.value = value;
    
    // Trigger events
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('blur', { bubbles: true }));
}

// Fill select field
function fillSelectField(element, value) {
    // Try to find matching option by value first
    let option = Array.from(element.options).find(opt => opt.value === value);
    
    // If not found, try to match by text
    if (!option) {
        option = Array.from(element.options).find(opt => 
            opt.text.toLowerCase().includes(value.toLowerCase()) ||
            value.toLowerCase().includes(opt.text.toLowerCase())
        );
    }
    
    if (option) {
        element.value = option.value;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
    }
    
    return false;
}

// Start smart learning mode
function startSmartLearning() {
    isSmartLearningActive = true;
    console.log('Smart learning mode activated');
    
    // Add visual indicator
    document.body.classList.add('learning-mode-active');
    
    // Setup event listeners for learning
    document.addEventListener('click', handleLearningClick, true);
    document.addEventListener('input', handleLearningInput, true);
    document.addEventListener('change', handleLearningChange, true);
}

// Stop smart learning mode
function stopSmartLearning() {
    isSmartLearningActive = false;
    console.log('Smart learning mode deactivated');
    
    // Remove visual indicator
    document.body.classList.remove('learning-mode-active');
    
    // Remove event listeners
    document.removeEventListener('click', handleLearningClick, true);
    document.removeEventListener('input', handleLearningInput, true);
    document.removeEventListener('change', handleLearningChange, true);
}

// Handle clicks during learning mode
function handleLearningClick(event) {
    if (!isSmartLearningActive) return;
    
    const element = event.target;
    
    // Check if it's a form element or button
    if (isFormElement(element) || isButton(element)) {
        console.log('Learning: Click on', element.tagName, element.type);
        
        // Send learning data to background
        chrome.runtime.sendMessage({
            action: 'learningEvent',
            data: {
                type: 'click',
                element: getElementInfo(element),
                timestamp: Date.now()
            }
        });
    }
}

// Handle input during learning mode
function handleLearningInput(event) {
    if (!isSmartLearningActive) return;
    
    const element = event.target;
    
    if (isFormElement(element)) {
        console.log('Learning: Input on', element.name || element.id, '=', element.value);
        
        chrome.runtime.sendMessage({
            action: 'learningEvent',
            data: {
                type: 'input',
                element: getElementInfo(element),
                value: element.value,
                timestamp: Date.now()
            }
        });
    }
}

// Handle change during learning mode
function handleLearningChange(event) {
    if (!isSmartLearningActive) return;
    
    const element = event.target;
    
    if (isFormElement(element)) {
        console.log('Learning: Change on', element.name || element.id, '=', element.value);
        
        chrome.runtime.sendMessage({
            action: 'learningEvent',
            data: {
                type: 'change',
                element: getElementInfo(element),
                value: element.value,
                timestamp: Date.now()
            }
        });
    }
}

// Setup mutation observer for dynamic content
function setupMutationObserver() {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if new forms were added
                        const newForms = node.querySelectorAll ? node.querySelectorAll('form') : [];
                        if (newForms.length > 0) {
                            console.log(`Detected ${newForms.length} new forms`);
                            detectFormsOnPage();
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// Helper functions
function getFieldLabel(field) {
    // Try different methods to find the label
    if (field.id) {
        const label = document.querySelector(`label[for="${field.id}"]`);
        if (label) return label.textContent.trim();
    }
    
    const parentLabel = field.closest('label');
    if (parentLabel) return parentLabel.textContent.trim();
    
    let sibling = field.previousElementSibling;
    while (sibling) {
        if (sibling.tagName === 'LABEL') {
            return sibling.textContent.trim();
        }
        sibling = sibling.previousElementSibling;
    }
    
    return field.placeholder || field.name || '';
}

function generateUniqueSelector(element) {
    if (element.id) {
        return `#${element.id}`;
    }
    
    if (element.name) {
        return `[name="${element.name}"]`;
    }
    
    // Generate path-based selector
    const path = [];
    let current = element;
    
    while (current && current !== document.body) {
        let selector = current.tagName.toLowerCase();
        
        if (current.className) {
            const classes = current.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                selector += '.' + classes.join('.');
            }
        }
        
        path.unshift(selector);
        current = current.parentElement;
        
        if (path.length > 4) break;
    }
    
    return path.join(' > ');
}

function isFormElement(element) {
    const formTags = ['input', 'select', 'textarea'];
    return formTags.includes(element.tagName.toLowerCase());
}

function isButton(element) {
    return element.tagName.toLowerCase() === 'button' || 
           (element.tagName.toLowerCase() === 'input' && 
            ['button', 'submit', 'reset'].includes(element.type));
}

function getElementInfo(element) {
    return {
        tagName: element.tagName.toLowerCase(),
        type: element.type || '',
        name: element.name || '',
        id: element.id || '',
        className: element.className || '',
        placeholder: element.placeholder || '',
        label: getFieldLabel(element),
        selector: generateUniqueSelector(element)
    };
}
