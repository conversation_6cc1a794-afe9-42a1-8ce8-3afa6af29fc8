# 🚀 دليل البدء السريع - نظام التعبئة التلقائية

## ⚡ اختبار سريع (5 دقائق)

### 1. تحضير البيانات
```bash
# استخدم ملف test-data.csv الموجود
# أو أنشئ ملف CSV بالتنسيق التالي:
```

```csv
اسم المنتج,رمز المنتج,سعر البيع,سعر الشراء,التصنيف,الوحدة
لابتوب ديل,DELL001,2500,2000,إلكترونيات,قطعة
قميص قطني,SHIRT001,85,60,ملابس,قطعة
أرز بسمتي,RICE001,25,20,أطعمة,كيلو
```

### 2. تحميل البيانات
1. **افتح popup.html**
2. **ارفع ملف test-data.csv**
3. **انقر "🚀 انتقل إلى واجهة التعبئة"**

### 3. تحميل النموذج
1. **في split-view، انقر "📋 تحميل نموذج محلي"**
2. **ستظهر رسالة نجاح** وسيظهر النموذج

### 4. ربط الحقول (مثال)
| الحقل في النموذج | العمود في البيانات |
|------------------|-------------------|
| اسم المنتج | اسم المنتج |
| رمز المنتج | رمز المنتج |
| سعر البيع | سعر البيع |
| سعر الشراء | سعر الشراء |
| التصنيف | التصنيف |
| الوحدة | الوحدة |

### 5. بدء التعبئة
1. **حدد صف واحد** من جدول البيانات
2. **تأكد من الرابط**: `https://quickly24erp.com/productservice/create`
3. **انقر "🚀 بدء التعبئة"**

## 🎯 النتائج المتوقعة

### ✅ نجح التحميل:
- رسالة: "تم تحميل النموذج بنجاح"
- ظهور النموذج مقسم لأقسام
- إمكانية ربط الحقول

### ✅ نجح الربط:
- رسالة: "تم ربط الحقل ... بالعمود ..."
- تغيير لون الحقل للأخضر
- ظهور قيمة تجريبية

### ✅ نجحت التعبئة:
- رسالة: "بدء عملية التعبئة التلقائية..."
- فتح نافذة جديدة
- تعبئة الحقول تلقائياً
- إرسال النموذج
- إغلاق النافذة

## 🔧 حل المشاكل السريع

### ❌ لم يتم تحميل النموذج:
```
خطأ: Error loading form template
```
**الحل السريع**: سيتم تحميل نموذج احتياطي تلقائياً

### ❌ لم تظهر الحقول:
**تحقق من**:
- وجود ملف `هيكل الموقع.html`
- صحة تنسيق الملف
- رسائل الخطأ في وحدة التحكم (F12)

### ❌ فشل الربط:
**تحقق من**:
- تحديد عمود من القائمة
- وجود بيانات في الجدول
- صحة أسماء الأعمدة

### ❌ فشل التعبئة:
**الأسباب المحتملة**:
- رابط خاطئ
- موقع لا يسمح بالتعبئة التلقائية
- تغيير في هيكل الموقع

## 📋 قائمة التحقق السريع

### قبل البدء:
- [ ] ملف CSV جاهز ومنسق
- [ ] ملف `هيكل الموقع.html` موجود
- [ ] الإضافة مثبتة ومفعلة
- [ ] الإنترنت متصل

### أثناء الاستخدام:
- [ ] تم تحميل البيانات بنجاح
- [ ] ظهر النموذج المحلي
- [ ] تم ربط الحقول المطلوبة
- [ ] تم تحديد الصفوف للتعبئة
- [ ] الرابط صحيح

### بعد التعبئة:
- [ ] تم تمييز الصفوف المكتملة
- [ ] ظهرت رسائل النجاح
- [ ] تم إنشاء المنتجات في الموقع
- [ ] تم حفظ التعيين (اختياري)

## 🎮 وضع التجريب

### للاختبار بدون موقع حقيقي:
1. **استخدم رابط تجريبي**: `https://httpbin.org/forms/post`
2. **أو استخدم صفحة محلية** للاختبار
3. **راقب وحدة التحكم** لرؤية البيانات المرسلة

### للاختبار مع بيانات وهمية:
```csv
المنتج,الكود,السعر
منتج تجريبي 1,TEST001,100
منتج تجريبي 2,TEST002,200
```

## 📞 الدعم السريع

### رسائل الخطأ الشائعة:
| الرسالة | السبب | الحل |
|---------|--------|------|
| "لا توجد بيانات محملة" | لم يتم رفع ملف | ارفع ملف CSV |
| "يرجى ربط الحقول" | لم يتم ربط أي حقل | اربط حقل واحد على الأقل |
| "يرجى تحديد صفوف" | لم يتم تحديد صفوف | حدد صف واحد على الأقل |

### للحصول على مساعدة:
1. **افتح وحدة التحكم** (F12)
2. **ابحث عن رسائل الخطأ** باللون الأحمر
3. **تحقق من رسائل النظام** في الأعلى
4. **راجع الدليل المفصل** في `FORM_AUTOMATION_GUIDE.md`

---

**💡 نصيحة**: ابدأ بصف واحد للاختبار، ثم زد العدد تدريجياً عند التأكد من عمل النظام بشكل صحيح.
