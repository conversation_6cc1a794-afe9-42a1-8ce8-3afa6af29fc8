# 💾 دليل إدارة التسجيلات - Smart Form Filler

## 🛑 كيفية إيقاف التسجيل وحفظ الخطوات

### 📋 الطريقة الأساسية

#### 1. **إيقاف التسجيل**
1. افتح إضافة Smart Form Filler
2. اذهب إلى قسم "📹 تسجيل الخطوات"
3. اضغط على زر **"⏹️ إيقاف التسجيل"**
4. ستظهر رسالة: "تم إيقاف التسجيل"
5. سيختفي المؤشر الأحمر من أعلى الشاشة

#### 2. **مراجعة الخطوات المسجلة**
بعد الإيقاف ستظهر تلقائياً:
- **عدد الخطوات**: "عدد الخطوات: X"
- **قائمة مفصلة** بكل خطوة مسجلة
- **أنواع العمليات**: نقرات، إدخال، تغييرات

#### 3. **خيارات الحفظ والإدارة**
ستظهر أزرار جديدة:
- **💾 حفظ التسجيل**: حفظ دائم مع اسم مخصص
- **📤 تصدير**: تصدير كملف JSON
- **🗑️ مسح**: مسح التسجيل الحالي

---

## 💾 أنواع الحفظ المتاحة

### 1. **الحفظ التلقائي**
- يتم **تلقائياً** عند إيقاف التسجيل
- يحفظ في **ذاكرة الإضافة** المؤقتة
- متاح للتشغيل الفوري

### 2. **الحفظ الدائم**
- اضغط **"💾 حفظ التسجيل"**
- أدخل **اسم مخصص** للتسجيل
- يحفظ في **قائمة التسجيلات المحفوظة**
- يبقى متاحاً حتى بعد إغلاق المتصفح

### 3. **التصدير كملف**
- اضغط **"📤 تصدير"**
- يتم تنزيل ملف **JSON**
- يمكن مشاركته أو نسخه احتياطياً
- قابل للاستيراد لاحقاً

---

## 📂 إدارة التسجيلات المحفوظة

### 🗂️ قسم "التسجيلات المحفوظة"

#### عرض التسجيلات:
كل تسجيل يظهر مع:
- **📝 الاسم**: اسم التسجيل المخصص
- **📅 التاريخ**: تاريخ ووقت الإنشاء
- **📊 عدد الخطوات**: إجمالي الخطوات المسجلة
- **🔗 عدد الروابط**: الحقول المربوطة
- **🏷️ الحالة**: محفوظ/نشط

#### الإجراءات المتاحة:
- **▶️ تشغيل**: تشغيل التسجيل مباشرة
- **✏️ تحميل**: تحميل للتعديل أو المراجعة
- **📤 تصدير**: تصدير كملف منفرد
- **🗑️ حذف**: حذف نهائي من القائمة

### 🔧 أدوات الإدارة العامة:
- **📥 استيراد تسجيل**: استيراد من ملف JSON
- **📦 تصدير الكل**: تصدير جميع التسجيلات

---

## 🎯 خطوات عملية مفصلة

### 📝 سيناريو كامل: من التسجيل إلى الحفظ

#### الخطوة 1: بدء التسجيل
```
1. اضغط "🔴 بدء التسجيل"
2. يظهر مؤشر أحمر: "🔴 جاري التسجيل... 0 خطوة"
3. قم بالعمليات المطلوبة
4. راقب العداد وهو يتزايد
```

#### الخطوة 2: إيقاف التسجيل
```
1. اضغط "⏹️ إيقاف التسجيل"
2. يختفي المؤشر الأحمر
3. تظهر رسالة: "تم تسجيل X خطوة"
4. يظهر قسم "الخطوات المسجلة"
```

#### الخطوة 3: مراجعة الخطوات
```
عدد الخطوات: 8

1. 🔴 نقر على زر الإنشاء: إنشاء منتج جديد
2. 📝 إدخال في حقل: اسم المنتج
3. 📝 إدخال في حقل: رمز المنتج
4. 🔢 إدخال في حقل: السعر
5. 📋 اختيار من قائمة: electronics
6. 📋 اختيار من قائمة: piece
7. 🔘 تغيير قيمة: physical
8. ✅ نقر على: حفظ المنتج
```

#### الخطوة 4: خيارات الحفظ
```
أ) الحفظ السريع:
   - اضغط "💾 حفظ التسجيل"
   - أدخل اسم: "تسجيل منتجات إلكترونية"
   - تأكيد الحفظ

ب) التصدير:
   - اضغط "📤 تصدير"
   - يتم تنزيل: recording_1703123456789.json

ج) المسح:
   - اضغط "🗑️ مسح"
   - تأكيد المسح
```

---

## 📁 تنسيق ملفات التسجيل

### 🗂️ محتويات ملف JSON:
```json
{
  "name": "تسجيل منتجات إلكترونية",
  "timestamp": 1703123456789,
  "totalSteps": 8,
  "url": "file:///test-smart-features.html",
  "fieldMappings": {
    "#product-name": {
      "type": "column",
      "column": "name"
    },
    "#product-sku": {
      "type": "column", 
      "column": "sku"
    }
  },
  "steps": [
    {
      "type": "click",
      "selector": ".create-button",
      "elementType": "button",
      "text": "إنشاء منتج جديد",
      "timestamp": 1703123456790,
      "isCreateButton": true
    }
  ],
  "version": "1.0"
}
```

---

## 🔄 استيراد وتصدير التسجيلات

### 📤 تصدير تسجيل واحد:
1. في قائمة التسجيلات المحفوظة
2. اضغط **"📤 تصدير"** بجانب التسجيل المطلوب
3. يتم تنزيل ملف: `اسم_التسجيل.json`

### 📦 تصدير جميع التسجيلات:
1. اضغط **"📦 تصدير الكل"**
2. يتم تنزيل ملف: `all_recordings_timestamp.json`
3. يحتوي على جميع التسجيلات المحفوظة

### 📥 استيراد تسجيل:
1. اضغط **"📥 استيراد تسجيل"**
2. اختر ملف JSON من جهازك
3. يتم تحميل التسجيل تلقائياً
4. يظهر في قائمة التسجيلات المحفوظة

---

## 🎮 تشغيل التسجيلات المحفوظة

### ▶️ التشغيل المباشر:
1. في قائمة التسجيلات المحفوظة
2. اضغط **"▶️ تشغيل"** بجانب التسجيل المطلوب
3. يتم تحميل التسجيل وتشغيله فوراً

### ✏️ التحميل للتعديل:
1. اضغط **"✏️ تحميل"** بجانب التسجيل
2. يتم تحميل الخطوات في قسم "الخطوات المسجلة"
3. يمكن مراجعتها أو تشغيلها أو تعديلها

---

## 🔍 استكشاف الأخطاء

### ❌ مشاكل شائعة وحلولها:

#### "لا توجد خطوات للحفظ"
- **السبب**: لم يتم تسجيل أي خطوات
- **الحل**: ابدأ تسجيل جديد وقم ببعض العمليات

#### "خطأ في حفظ التسجيل"
- **السبب**: مشكلة في التخزين المحلي
- **الحل**: تحقق من مساحة التخزين أو أعد تحميل الصفحة

#### "خطأ في قراءة ملف التسجيل"
- **السبب**: ملف JSON تالف أو غير صحيح
- **الحل**: تأكد من صحة الملف أو جرب ملف آخر

#### التسجيل لا يعمل بعد الاستيراد
- **السبب**: اختلاف في بنية الصفحة
- **الحل**: تحقق من أن الصفحة مطابقة للتسجيل الأصلي

---

## 💡 نصائح للاستخدام الأمثل

### 🎯 أفضل الممارسات:

#### للتسجيل:
- **أسماء واضحة**: استخدم أسماء وصفية للتسجيلات
- **تسجيلات قصيرة**: قسم العمليات الطويلة لتسجيلات متعددة
- **اختبار فوري**: اختبر التسجيل فور إنشائه

#### للحفظ:
- **نسخ احتياطية**: صدر التسجيلات المهمة كملفات
- **تنظيم الأسماء**: استخدم نظام تسمية منطقي
- **مراجعة دورية**: احذف التسجيلات القديمة غير المستخدمة

#### للتشغيل:
- **بيانات محدثة**: تأكد من تحميل البيانات المناسبة
- **صفحة صحيحة**: تأكد من أن الصفحة مطابقة للتسجيل
- **مراقبة العملية**: راقب التشغيل للتأكد من صحته

---

## 📊 إحصائيات التسجيلات

### 📈 معلومات مفيدة:
- **الحد الأقصى**: 20 تسجيل محفوظ
- **حجم الملف**: عادة أقل من 50KB لكل تسجيل
- **التوافق**: يعمل مع جميع إصدارات الإضافة
- **الأمان**: البيانات محفوظة محلياً فقط

---

## 🎉 الخلاصة

الآن لديك نظام شامل لإدارة التسجيلات:
- ✅ **إيقاف سهل** مع حفظ تلقائي
- ✅ **حفظ دائم** مع أسماء مخصصة  
- ✅ **تصدير واستيراد** للمشاركة والنسخ الاحتياطي
- ✅ **إدارة كاملة** للتسجيلات المحفوظة
- ✅ **تشغيل مرن** للتسجيلات المختلفة

استمتع بالنظام الجديد المحسن! 🚀
