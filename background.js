// Smart Form Filler - <PERSON> Script (Service Worker)

// Extension installation and update handling
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('Smart Form Filler installed');
        
        // Initialize default settings
        chrome.storage.local.set({
            settings: {
                autoDetectFields: true,
                smartLearning: true,
                fillDelay: 500,
                language: 'ar'
            }
        });
        
        // Open welcome page
        chrome.tabs.create({
            url: chrome.runtime.getURL('popup.html')
        });
    } else if (details.reason === 'update') {
        console.log('Smart Form Filler updated to version', chrome.runtime.getManifest().version);
    }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // This will open the popup, but we can add additional logic here if needed
    console.log('Extension icon clicked on tab:', tab.url);
});

// Message handling between different parts of the extension
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background received message:', request);
    
    switch (request.action) {
        case 'openSplitView':
            handleOpenSplitView(request.data, sendResponse);
            break;
            
        case 'saveFormPattern':
            handleSaveFormPattern(request.data, sendResponse);
            break;
            
        case 'getFormPatterns':
            handleGetFormPatterns(sendResponse);
            break;
            
        case 'detectFormFields':
            handleDetectFormFields(sender.tab.id, sendResponse);
            break;
            
        case 'fillFormData':
            handleFillFormData(sender.tab.id, request.data, sendResponse);
            break;
            
        default:
            console.log('Unknown action:', request.action);
            sendResponse({ success: false, error: 'Unknown action' });
    }
    
    // Return true to indicate we will send a response asynchronously
    return true;
});

// Handle opening split view
function handleOpenSplitView(data, sendResponse) {
    chrome.tabs.create({
        url: chrome.runtime.getURL('split-view.html')
    }, (tab) => {
        if (chrome.runtime.lastError) {
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            sendResponse({ success: true, tabId: tab.id });
        }
    });
}

// Handle saving form patterns
function handleSaveFormPattern(patternData, sendResponse) {
    chrome.storage.local.get(['formPatterns'], (result) => {
        const patterns = result.formPatterns || [];
        
        // Add timestamp and unique ID
        patternData.id = Date.now().toString();
        patternData.createdAt = new Date().toISOString();
        
        patterns.push(patternData);
        
        chrome.storage.local.set({ formPatterns: patterns }, () => {
            if (chrome.runtime.lastError) {
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                sendResponse({ success: true, patternId: patternData.id });
            }
        });
    });
}

// Handle getting form patterns
function handleGetFormPatterns(sendResponse) {
    chrome.storage.local.get(['formPatterns'], (result) => {
        if (chrome.runtime.lastError) {
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            sendResponse({ success: true, patterns: result.formPatterns || [] });
        }
    });
}

// Handle form field detection
function handleDetectFormFields(tabId, sendResponse) {
    chrome.scripting.executeScript({
        target: { tabId: tabId },
        function: detectFormFieldsInPage
    }, (results) => {
        if (chrome.runtime.lastError) {
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else if (results && results[0]) {
            sendResponse({ success: true, fields: results[0].result });
        } else {
            sendResponse({ success: false, error: 'No results from field detection' });
        }
    });
}

// Handle form data filling
function handleFillFormData(tabId, fillData, sendResponse) {
    chrome.scripting.executeScript({
        target: { tabId: tabId },
        function: fillFormWithData,
        args: [fillData]
    }, (results) => {
        if (chrome.runtime.lastError) {
            sendResponse({ success: false, error: chrome.runtime.lastError.message });
        } else {
            sendResponse({ success: true, result: results[0]?.result });
        }
    });
}

// Function to be injected into pages for field detection
function detectFormFieldsInPage() {
    const fields = [];
    
    // Find all form elements
    const forms = document.querySelectorAll('form');
    
    forms.forEach((form, formIndex) => {
        // Find input fields
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach((input, inputIndex) => {
            const fieldInfo = {
                formIndex: formIndex,
                inputIndex: inputIndex,
                type: input.type || input.tagName.toLowerCase(),
                name: input.name || '',
                id: input.id || '',
                placeholder: input.placeholder || '',
                label: getFieldLabel(input),
                required: input.required || false,
                selector: generateSelector(input)
            };
            
            fields.push(fieldInfo);
        });
    });
    
    return fields;
}

// Function to be injected into pages for form filling
function fillFormWithData(fillData) {
    const results = [];
    
    fillData.mappings.forEach(mapping => {
        try {
            const element = document.querySelector(mapping.selector);
            if (element) {
                if (element.type === 'select-one') {
                    // Handle select dropdowns
                    const option = Array.from(element.options).find(opt => 
                        opt.value === mapping.value || opt.text === mapping.value
                    );
                    if (option) {
                        element.value = option.value;
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                } else {
                    // Handle regular inputs
                    element.value = mapping.value;
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                }
                
                results.push({ selector: mapping.selector, success: true });
            } else {
                results.push({ selector: mapping.selector, success: false, error: 'Element not found' });
            }
        } catch (error) {
            results.push({ selector: mapping.selector, success: false, error: error.message });
        }
    });
    
    return results;
}

// Helper function to get field label
function getFieldLabel(input) {
    // Try to find associated label
    if (input.id) {
        const label = document.querySelector(`label[for="${input.id}"]`);
        if (label) return label.textContent.trim();
    }
    
    // Try to find parent label
    const parentLabel = input.closest('label');
    if (parentLabel) return parentLabel.textContent.trim();
    
    // Try to find previous sibling label
    let sibling = input.previousElementSibling;
    while (sibling) {
        if (sibling.tagName === 'LABEL') {
            return sibling.textContent.trim();
        }
        sibling = sibling.previousElementSibling;
    }
    
    return '';
}

// Helper function to generate CSS selector
function generateSelector(element) {
    if (element.id) {
        return `#${element.id}`;
    }
    
    if (element.name) {
        return `[name="${element.name}"]`;
    }
    
    // Generate path-based selector as fallback
    const path = [];
    let current = element;
    
    while (current && current.tagName) {
        let selector = current.tagName.toLowerCase();
        
        if (current.className) {
            selector += '.' + current.className.split(' ').join('.');
        }
        
        path.unshift(selector);
        current = current.parentElement;
        
        if (path.length > 5) break; // Limit depth
    }
    
    return path.join(' > ');
}

// Handle tab updates to maintain state
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        console.log('Tab updated:', tab.url);
        // Could add logic here to auto-detect forms or restore state
    }
});

// Clean up storage periodically
chrome.alarms.create('cleanup', { periodInMinutes: 60 });

chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'cleanup') {
        // Clean up old temporary data
        chrome.storage.local.get(null, (items) => {
            const keysToRemove = [];
            const now = Date.now();
            
            Object.keys(items).forEach(key => {
                if (key.startsWith('temp_') && items[key].timestamp) {
                    // Remove temporary data older than 24 hours
                    if (now - items[key].timestamp > 24 * 60 * 60 * 1000) {
                        keysToRemove.push(key);
                    }
                }
            });
            
            if (keysToRemove.length > 0) {
                chrome.storage.local.remove(keysToRemove);
                console.log('Cleaned up', keysToRemove.length, 'temporary items');
            }
        });
    }
});
