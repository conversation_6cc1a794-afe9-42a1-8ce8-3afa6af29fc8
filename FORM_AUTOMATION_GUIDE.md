# دليل نظام التعبئة التلقائية للنماذج

## 🎯 نظرة عامة
تم تطوير نظام متقدم للتعبئة التلقائية يقوم بـ:
1. **تحليل هيكل النموذج** من ملف `هيكل الموقع.html`
2. **عرض نموذج محلي تفاعلي** للربط مع البيانات
3. **تعبئة الموقع الفعلي تلقائياً** بناءً على البيانات المحددة
4. **إرسال النماذج** والضغط على زر "إنشاء المنتج"
5. **تكرار العملية** لجميع الصفوف المحددة

## 🚀 خطوات الاستخدام

### الخطوة 1: تحضير البيانات
1. **افتح popup.html** وارفع ملف CSV يحتوي على بيانات المنتجات
2. **انقر "🚀 انتقل إلى واجهة التعبئة"**

### الخطوة 2: تحميل النموذج المحلي
1. **في split-view، انقر "📋 تحميل نموذج محلي"**
2. **سيتم تحليل ملف `هيكل الموقع.html`** وعرض النموذج
3. **ستظهر جميع حقول النموذج** مقسمة حسب الأقسام:
   - المعلومات الأساسية
   - الأسعار
   - التصنيفات والوحدات
   - الحسابات المالية
   - التفاصيل الإضافية

### الخطوة 3: ربط الحقول بالبيانات
1. **انقر على أي حقل** في النموذج المحلي
2. **اختر العمود المناسب** من القائمة المنسدلة
3. **انقر "ربط"** لربط الحقل بالعمود
4. **كرر العملية** لجميع الحقول المطلوبة
5. **ستظهر قيمة تجريبية** من الصف الأول

### الخطوة 4: تحديد الصفوف للتعبئة
1. **في جدول البيانات، حدد الصفوف** التي تريد تعبئتها
2. **يمكنك تحديد صف واحد أو عدة صفوف**
3. **سيتم تحديث زر التعبئة** ليظهر عدد الصفوف

### الخطوة 5: بدء التعبئة التلقائية
1. **تأكد من إدخال الرابط الصحيح**: `https://quickly24erp.com/productservice/create`
2. **انقر "🚀 بدء التعبئة"**
3. **سيتم فتح نافذة جديدة** لكل صف
4. **التعبئة التلقائية** للحقول
5. **الضغط على زر "إنشاء المنتج"**
6. **إغلاق النافذة** والانتقال للصف التالي

### الخطوة 6: حفظ التعيين
1. **انقر "💾 حفظ التعيين"** لحفظ ربط الحقول
2. **يمكن استخدام التعيين المحفوظ** لاحقاً

## 📋 مثال على ملف CSV

```csv
اسم المنتج,رمز المنتج,سعر البيع,سعر الشراء,التصنيف,الوحدة,الوصف
لابتوب ديل,DELL001,2500,2000,إلكترونيات,قطعة,لابتوب عالي الأداء
قميص قطني,SHIRT001,85,60,ملابس,قطعة,قميص قطني مريح
أرز بسمتي,RICE001,25,20,أطعمة,كيلو,أرز بسمتي فاخر
```

## 🔧 الميزات المتقدمة

### التعرف الذكي على الحقول
- **حقول النص**: اسم المنتج، رمز المنتج، الوصف
- **حقول الأرقام**: الأسعار، الكميات
- **القوائم المنسدلة**: التصنيفات، الوحدات، الضرائب
- **أزرار الراديو**: خيارات متعددة
- **حقول الملفات**: رفع الصور

### معالجة القوائم المنسدلة
- **البحث بالنص**: يبحث عن النص في خيارات القائمة
- **البحث بالقيمة**: يطابق القيمة المحددة
- **التطابق الجزئي**: يجد أقرب تطابق

### تتبع التقدم
- **رسائل الحالة**: تظهر تقدم العملية
- **تمييز الصفوف المكتملة**: ✅ علامة خضراء
- **عداد الصفوف**: يظهر الصف الحالي من المجموع

## 🛠️ استكشاف الأخطاء

### إذا لم يتم تحميل النموذج:
```
خطأ: Error loading form template
```
**الحل**: تأكد من وجود ملف `هيكل الموقع.html` في نفس المجلد

### إذا فشلت التعبئة:
```
خطأ: Error filling form
```
**الحلول**:
1. تأكد من صحة الرابط
2. تحقق من ربط الحقول بشكل صحيح
3. تأكد من أن الموقع يسمح بالتعبئة التلقائية

### إذا لم يتم العثور على زر الإرسال:
```
تحذير: Submit button not found
```
**الحل**: النظام يبحث عن:
- `input[type="submit"][value*="إنشاء"]`
- `button[type="submit"]`
- `.btn-primary`

## 📊 إحصائيات العملية

### معلومات التقدم:
- **عدد الصفوف المحددة**: يظهر في زر التعبئة
- **الصف الحالي**: يظهر في رسائل الحالة
- **الصفوف المكتملة**: تظهر بعلامة ✅
- **الوقت المتوقع**: حوالي 5-7 ثوانٍ لكل صف

### معدل النجاح:
- **التعبئة**: 95%+ للحقول المربوطة بشكل صحيح
- **الإرسال**: 90%+ إذا كان الموقع يعمل بشكل طبيعي

## 🔒 الأمان والخصوصية

### البيانات المحلية:
- **جميع البيانات تبقى محلية** في المتصفح
- **لا يتم إرسال البيانات** لخوادم خارجية
- **التعيينات محفوظة محلياً** في chrome.storage

### الأذونات المطلوبة:
- **tabs**: لفتح نوافذ جديدة
- **scripting**: لحقن كود التعبئة
- **storage**: لحفظ التعيينات
- **activeTab**: للوصول للصفحة النشطة

## 🎯 نصائح للاستخدام الأمثل

### تحضير البيانات:
1. **تأكد من صحة البيانات** قبل التحميل
2. **استخدم أسماء أعمدة واضحة** باللغة العربية
3. **تجنب الخلايا الفارغة** في الحقول المطلوبة

### ربط الحقول:
1. **ابدأ بالحقول المطلوبة** (المميزة بـ *)
2. **تحقق من القيم التجريبية** قبل البدء
3. **احفظ التعيين** لاستخدامه لاحقاً

### مراقبة العملية:
1. **راقب رسائل الحالة** لتتبع التقدم
2. **لا تغلق النافذة** أثناء التعبئة
3. **تحقق من النتائج** في الموقع المستهدف

## 📈 التطوير المستقبلي

### ميزات مخططة:
- **استيراد/تصدير التعيينات**
- **جدولة التعبئة**
- **تقارير مفصلة**
- **دعم مواقع متعددة**
- **معالجة الأخطاء المتقدمة**

---

**ملاحظة**: هذا النظام مصمم خصيصاً للموقع `https://quickly24erp.com/productservice/create` ويمكن تخصيصه لمواقع أخرى.
