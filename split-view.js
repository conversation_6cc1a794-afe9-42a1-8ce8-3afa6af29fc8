// Smart Form Filler - Split View Script
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const dataTableContainer = document.getElementById('data-table-container');
    const websiteFrame = document.getElementById('website-frame');
    const websiteUrl = document.getElementById('website-url');
    const loadWebsiteBtn = document.getElementById('load-website');
    const refreshWebsiteBtn = document.getElementById('refresh-website');
    const loadFormTemplateBtn = document.getElementById('load-form-template');
    const detectFieldsBtn = document.getElementById('detect-fields');
    const startFillingBtn = document.getElementById('start-filling');
    const saveMappingBtn = document.getElementById('save-mapping');
    const formTemplateContainer = document.getElementById('form-template-container');
    const templateContent = document.getElementById('template-content');
    const toggleTemplateBtn = document.getElementById('toggle-template');
    const resizeHandle = document.getElementById('resize-handle');
    
    // Stats elements
    const totalRowsElement = document.getElementById('total-rows');
    const totalColumnsElement = document.getElementById('total-columns');
    const selectedRowsElement = document.getElementById('selected-rows');

    // State variables
    let uploadedData = null;
    let selectedRows = new Set();
    let fieldMappings = {};
    let isResizing = false;
    let savedUrl = '';

    // Initialize
    init();

    function init() {
        // Load uploaded data from storage
        loadUploadedData();
        
        // Setup event listeners
        setupEventListeners();
        
        // Load saved URL
        loadSavedUrl();
    }

    function setupEventListeners() {
        loadWebsiteBtn.addEventListener('click', loadWebsite);
        refreshWebsiteBtn.addEventListener('click', refreshWebsite);
        loadFormTemplateBtn.addEventListener('click', loadFormTemplate);
        detectFieldsBtn.addEventListener('click', detectFields);
        startFillingBtn.addEventListener('click', startFilling);
        saveMappingBtn.addEventListener('click', saveMapping);
        toggleTemplateBtn.addEventListener('click', toggleTemplate);
        
        // URL input events
        websiteUrl.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadWebsite();
            }
        });
        
        websiteUrl.addEventListener('input', saveUrl);
        
        // Resize functionality
        setupResizeHandle();
    }

    function loadUploadedData() {
        chrome.storage.local.get(['uploadedData'], function(result) {
            if (result.uploadedData) {
                uploadedData = result.uploadedData;
                displayDataTable();
                updateStats();
            } else {
                showNoDataMessage();
            }
        });
    }

    function displayDataTable() {
        if (!uploadedData || !uploadedData.data || uploadedData.data.length === 0) {
            showNoDataMessage();
            return;
        }

        const data = uploadedData.data;
        const columns = uploadedData.columns;

        // Create table
        const table = document.createElement('table');
        table.className = 'data-table';
        table.id = 'data-viewer-table';

        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // Add checkbox column
        const checkboxHeader = document.createElement('th');
        checkboxHeader.innerHTML = '<input type="checkbox" id="select-all" class="row-selector">';
        headerRow.appendChild(checkboxHeader);

        // Add data columns
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Create body
        const tbody = document.createElement('tbody');
        
        data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.dataset.rowIndex = index;

            // Add checkbox cell
            const checkboxCell = document.createElement('td');
            checkboxCell.innerHTML = `<input type="checkbox" class="row-selector" data-row="${index}">`;
            tr.appendChild(checkboxCell);

            // Add data cells
            columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                td.dataset.column = column;
                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });

        table.appendChild(tbody);

        // Clear container and add table
        dataTableContainer.innerHTML = '';
        dataTableContainer.appendChild(table);

        // Setup table event listeners
        setupTableEventListeners();
    }

    function setupTableEventListeners() {
        const selectAllCheckbox = document.getElementById('select-all');
        const rowCheckboxes = document.querySelectorAll('.row-selector[data-row]');

        // Select all functionality
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const rowIndex = parseInt(checkbox.dataset.row);
                if (isChecked) {
                    selectedRows.add(rowIndex);
                    checkbox.closest('tr').classList.add('selected');
                } else {
                    selectedRows.delete(rowIndex);
                    checkbox.closest('tr').classList.remove('selected');
                }
            });
            updateSelectedCount();
        });

        // Individual row selection
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const rowIndex = parseInt(this.dataset.row);
                const row = this.closest('tr');
                
                if (this.checked) {
                    selectedRows.add(rowIndex);
                    row.classList.add('selected');
                } else {
                    selectedRows.delete(rowIndex);
                    row.classList.remove('selected');
                }
                
                updateSelectedCount();
                
                // Update select all checkbox
                selectAllCheckbox.checked = selectedRows.size === rowCheckboxes.length;
            });
        });

        // Right-click context menu for field mapping
        const dataCells = document.querySelectorAll('.data-table td[data-column]');
        dataCells.forEach(cell => {
            cell.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                showColumnContextMenu(e, this.dataset.column, this.textContent);
            });
        });
    }

    function showColumnContextMenu(event, columnName, cellValue) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Create context menu
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.innerHTML = `
            <div class="context-menu-header">تعيين العمود: ${columnName}</div>
            <div class="context-menu-item" data-action="map-column" data-column="${columnName}">
                <span class="column-icon">📋</span>
                تعيين هذا العمود للحقل المحدد
            </div>
            <div class="context-menu-item custom-value" data-action="custom-value">
                <span class="custom-icon">✏️</span>
                استخدام قيمة مخصصة
            </div>
            <div class="context-menu-item" data-action="copy-value">
                <span class="column-icon">📄</span>
                نسخ القيمة: ${cellValue}
            </div>
        `;

        // Position menu
        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';

        // Add to page
        document.body.appendChild(menu);

        // Add event listeners
        menu.addEventListener('click', function(e) {
            const action = e.target.closest('.context-menu-item')?.dataset.action;
            const column = e.target.closest('.context-menu-item')?.dataset.column;
            
            if (action === 'map-column') {
                mapColumnToField(column);
            } else if (action === 'custom-value') {
                showCustomValueInput();
            } else if (action === 'copy-value') {
                copyToClipboard(cellValue);
            }
            
            menu.remove();
        });

        // Remove menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function removeMenu() {
                menu.remove();
                document.removeEventListener('click', removeMenu);
            });
        }, 100);
    }

    function loadWebsite() {
        const url = websiteUrl.value.trim();
        if (!url) {
            showNotification('يرجى إدخال رابط الموقع', 'warning');
            return;
        }

        // Add protocol if missing
        let formattedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            formattedUrl = 'https://' + url;
        }

        websiteFrame.src = formattedUrl;
        saveUrl();
        showNotification('جاري تحميل الموقع...', 'info');

        // Setup iframe load listener to analyze page structure
        websiteFrame.onload = function() {
            setTimeout(() => {
                // Check if it's a local file or external URL
                if (formattedUrl.startsWith('file://') || formattedUrl.includes('test-page-analysis.html')) {
                    analyzeLocalPage();
                } else {
                    analyzePageStructure();
                }
                showNotification('تم تحميل الصفحة', 'success');
            }, 1000);
        };
    }

    function refreshWebsite() {
        if (savedUrl) {
            websiteUrl.value = savedUrl;
            websiteFrame.src = savedUrl;
            showNotification('تم تحديث الصفحة', 'success');
        } else {
            loadWebsite();
        }
    }

    function saveUrl() {
        savedUrl = websiteUrl.value.trim();
        chrome.storage.local.set({ savedUrl: savedUrl });
    }

    function loadSavedUrl() {
        chrome.storage.local.get(['savedUrl'], function(result) {
            if (result.savedUrl) {
                savedUrl = result.savedUrl;
                websiteUrl.value = savedUrl;
            }
        });
    }

    function showNoDataMessage() {
        dataTableContainer.innerHTML = `
            <div class="no-data">
                <p>لا توجد بيانات محملة</p>
                <p>يرجى العودة إلى النافذة الرئيسية وتحميل ملف البيانات</p>
            </div>
        `;
    }

    function updateStats() {
        if (uploadedData) {
            totalRowsElement.textContent = uploadedData.rowCount || 0;
            totalColumnsElement.textContent = uploadedData.columns?.length || 0;
        }
    }

    function updateSelectedCount() {
        selectedRowsElement.textContent = selectedRows.size;
        updateFillingButtonState();
    }

    function setupResizeHandle() {
        resizeHandle.addEventListener('mousedown', function(_e) {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            
            const containerWidth = window.innerWidth;
            const newDataPanelWidth = (e.clientX / containerWidth) * 100;
            
            if (newDataPanelWidth >= 20 && newDataPanelWidth <= 80) {
                document.querySelector('.data-panel').style.width = newDataPanelWidth + '%';
                document.querySelector('.website-panel').style.width = (100 - newDataPanelWidth) + '%';
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    // Page structure analysis functions
    function analyzePageStructure() {
        try {
            // Try to access iframe document
            let iframeDoc = null;
            try {
                iframeDoc = websiteFrame.contentDocument || websiteFrame.contentWindow.document;
            } catch (corsError) {
                console.log('CORS error detected:', corsError.message);
                // Use content script injection instead
                analyzePageWithContentScript();
                return;
            }

            if (!iframeDoc) {
                showNotification('لا يمكن الوصول إلى محتوى الصفحة - جاري المحاولة بطريقة بديلة...', 'info');
                analyzePageWithContentScript();
                return;
            }

            const pageStructure = {
                url: websiteFrame.src,
                title: iframeDoc.title,
                forms: [],
                buttons: [],
                inputs: [],
                selects: [],
                textareas: [],
                links: [],
                images: [],
                timestamp: Date.now()
            };

            // Analyze forms
            const forms = iframeDoc.querySelectorAll('form');
            forms.forEach((form, index) => {
                const formInfo = {
                    index: index,
                    id: form.id || '',
                    name: form.name || '',
                    action: form.action || '',
                    method: form.method || 'GET',
                    fields: [],
                    selector: generateSelector(form, iframeDoc)
                };

                // Analyze form fields
                const fields = form.querySelectorAll('input, select, textarea');
                fields.forEach((field, fieldIndex) => {
                    const fieldInfo = analyzeFormField(field, fieldIndex, iframeDoc);
                    formInfo.fields.push(fieldInfo);

                    // Add to global arrays
                    if (field.tagName.toLowerCase() === 'input') {
                        pageStructure.inputs.push(fieldInfo);
                    } else if (field.tagName.toLowerCase() === 'select') {
                        pageStructure.selects.push(fieldInfo);
                    } else if (field.tagName.toLowerCase() === 'textarea') {
                        pageStructure.textareas.push(fieldInfo);
                    }
                });

                pageStructure.forms.push(formInfo);
            });

            // Analyze buttons (including those outside forms)
            const buttons = iframeDoc.querySelectorAll('button, input[type="button"], input[type="submit"], input[type="reset"]');
            buttons.forEach((button, index) => {
                const buttonInfo = analyzeButton(button, index, iframeDoc);
                pageStructure.buttons.push(buttonInfo);
            });

            // Analyze links
            const links = iframeDoc.querySelectorAll('a[href]');
            links.forEach((link, index) => {
                if (index < 20) { // Limit to first 20 links
                    pageStructure.links.push({
                        index: index,
                        text: link.textContent.trim(),
                        href: link.href,
                        target: link.target || '',
                        selector: generateSelector(link, iframeDoc)
                    });
                }
            });

            // Analyze images
            const images = iframeDoc.querySelectorAll('img');
            images.forEach((img, index) => {
                if (index < 10) { // Limit to first 10 images
                    pageStructure.images.push({
                        index: index,
                        src: img.src,
                        alt: img.alt || '',
                        width: img.width || 0,
                        height: img.height || 0,
                        selector: generateSelector(img, iframeDoc)
                    });
                }
            });

            // Store the analysis
            chrome.storage.local.set({
                pageStructure: pageStructure
            });

            // Display analysis results
            displayPageAnalysis(pageStructure);

            // Highlight detected elements
            highlightDetectedElements(iframeDoc, pageStructure);

            console.log('Page structure analyzed:', pageStructure);

        } catch (error) {
            console.error('Error analyzing page structure:', error);
            showNotification('خطأ في تحليل هيكل الصفحة: ' + error.message, 'error');
        }
    }

    function analyzeFormField(field, index, doc) {
        const fieldInfo = {
            index: index,
            tagName: field.tagName.toLowerCase(),
            type: field.type || '',
            name: field.name || '',
            id: field.id || '',
            placeholder: field.placeholder || '',
            value: field.value || '',
            required: field.required || false,
            disabled: field.disabled || false,
            readonly: field.readOnly || false,
            maxLength: field.maxLength || 0,
            label: findFieldLabel(field, doc),
            selector: generateSelector(field, doc),
            boundingRect: field.getBoundingClientRect()
        };

        // Special handling for select elements
        if (field.tagName.toLowerCase() === 'select') {
            fieldInfo.options = Array.from(field.options).map(option => ({
                value: option.value,
                text: option.text,
                selected: option.selected
            }));
            fieldInfo.multiple = field.multiple;
        }

        // Special handling for input elements
        if (field.tagName.toLowerCase() === 'input') {
            fieldInfo.min = field.min || '';
            fieldInfo.max = field.max || '';
            fieldInfo.step = field.step || '';
            fieldInfo.pattern = field.pattern || '';
        }

        return fieldInfo;
    }

    function analyzeButton(button, index, doc) {
        return {
            index: index,
            tagName: button.tagName.toLowerCase(),
            type: button.type || '',
            text: button.textContent.trim() || button.value || '',
            name: button.name || '',
            id: button.id || '',
            disabled: button.disabled || false,
            formAction: button.formAction || '',
            formMethod: button.formMethod || '',
            selector: generateSelector(button, doc),
            boundingRect: button.getBoundingClientRect()
        };
    }

    function findFieldLabel(field, doc) {
        // Try to find associated label by 'for' attribute
        if (field.id) {
            const label = doc.querySelector(`label[for="${field.id}"]`);
            if (label) return label.textContent.trim();
        }

        // Try to find parent label
        let parent = field.parentElement;
        while (parent && parent !== doc.body) {
            if (parent.tagName.toLowerCase() === 'label') {
                return parent.textContent.trim();
            }
            parent = parent.parentElement;
        }

        // Try to find previous sibling label
        let sibling = field.previousElementSibling;
        while (sibling) {
            if (sibling.tagName.toLowerCase() === 'label') {
                return sibling.textContent.trim();
            }
            sibling = sibling.previousElementSibling;
        }

        // Try to find nearby text
        const nearbyText = findNearbyText(field, doc);
        return nearbyText || field.placeholder || field.name || '';
    }

    function findNearbyText(element, doc) {
        const rect = element.getBoundingClientRect();
        const textNodes = [];

        // Find text nodes near the element
        const walker = doc.createTreeWalker(
            doc.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            const text = node.textContent.trim();
            if (text.length > 2 && text.length < 50) {
                const range = doc.createRange();
                range.selectNode(node);
                const nodeRect = range.getBoundingClientRect();

                // Check if text is near the element
                const distance = Math.sqrt(
                    Math.pow(nodeRect.left - rect.left, 2) +
                    Math.pow(nodeRect.top - rect.top, 2)
                );

                if (distance < 100) {
                    textNodes.push({ text: text, distance: distance });
                }
            }
        }

        // Return the closest text
        textNodes.sort((a, b) => a.distance - b.distance);
        return textNodes.length > 0 ? textNodes[0].text : '';
    }

    function generateSelector(element, doc) {
        // Try ID first
        if (element.id) {
            return `#${element.id}`;
        }

        // Try name attribute
        if (element.name) {
            return `[name="${element.name}"]`;
        }

        // Try class-based selector
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }

        // Generate path-based selector
        const path = [];
        let current = element;

        while (current && current !== doc.body && path.length < 5) {
            let selector = current.tagName.toLowerCase();

            // Add index if there are siblings with same tag
            const siblings = Array.from(current.parentElement?.children || [])
                .filter(sibling => sibling.tagName === current.tagName);

            if (siblings.length > 1) {
                const index = siblings.indexOf(current) + 1;
                selector += `:nth-of-type(${index})`;
            }

            path.unshift(selector);
            current = current.parentElement;
        }

        return path.join(' > ');
    }

    // Load and display form template
    function loadFormTemplate() {
        showNotification('جاري تحميل نموذج المنتج...', 'info');

        // Load form structure from file
        fetch('هيكل الموقع .html')
            .then(response => response.text())
            .then(html => {
                parseAndDisplayFormTemplate(html);
                formTemplateContainer.style.display = 'block';
                showNotification('تم تحميل النموذج بنجاح', 'success');
            })
            .catch(error => {
                console.error('Error loading form template:', error);
                // Fallback to hardcoded template
                createHardcodedTemplate();
                formTemplateContainer.style.display = 'block';
                showNotification('تم تحميل النموذج الاحتياطي', 'warning');
            });
    }

    // Parse HTML and create interactive form template
    function parseAndDisplayFormTemplate(html) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const formFields = [];

        // Extract input fields
        const inputs = doc.querySelectorAll('input[type="text"], input[type="number"], input[type="file"]');
        inputs.forEach(input => {
            const label = findLabelText(input, doc);
            const required = input.hasAttribute('required');

            formFields.push({
                type: 'input',
                inputType: input.type,
                name: input.name,
                id: input.id,
                label: label,
                placeholder: input.placeholder,
                required: required,
                element: input
            });
        });

        // Extract select fields
        const selects = doc.querySelectorAll('select');
        selects.forEach(select => {
            const label = findLabelText(select, doc);
            const required = select.hasAttribute('required');
            const options = Array.from(select.options).map(option => ({
                value: option.value,
                text: option.text
            }));

            formFields.push({
                type: 'select',
                name: select.name,
                id: select.id,
                label: label,
                required: required,
                options: options,
                element: select
            });
        });

        // Extract textarea fields
        const textareas = doc.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            const label = findLabelText(textarea, doc);

            formFields.push({
                type: 'textarea',
                name: textarea.name,
                id: textarea.id,
                label: label,
                placeholder: textarea.placeholder,
                required: false,
                element: textarea
            });
        });

        // Extract radio buttons
        const radios = doc.querySelectorAll('input[type="radio"]');
        const radioGroups = {};
        radios.forEach(radio => {
            if (!radioGroups[radio.name]) {
                const label = findLabelText(radio, doc);
                radioGroups[radio.name] = {
                    type: 'radio',
                    name: radio.name,
                    label: label,
                    options: [],
                    required: false
                };
            }

            const optionLabel = radio.nextElementSibling ? radio.nextElementSibling.textContent.trim() : radio.value;
            radioGroups[radio.name].options.push({
                value: radio.value,
                text: optionLabel,
                checked: radio.checked
            });
        });

        // Add radio groups to form fields
        Object.values(radioGroups).forEach(group => {
            formFields.push(group);
        });

        // Display the form template
        displayFormTemplate(formFields);
    }

    // Find label text for form elements
    function findLabelText(element, doc) {
        // Try to find label by 'for' attribute
        if (element.id) {
            const label = doc.querySelector(`label[for="${element.id}"]`);
            if (label) return label.textContent.trim().replace('*', '');
        }

        // Try to find parent label
        let parent = element.parentElement;
        while (parent && parent !== doc.body) {
            if (parent.tagName.toLowerCase() === 'label') {
                return parent.textContent.trim().replace('*', '');
            }
            parent = parent.parentElement;
        }

        // Try to find previous sibling label
        let sibling = element.previousElementSibling;
        while (sibling) {
            if (sibling.tagName.toLowerCase() === 'label') {
                return sibling.textContent.trim().replace('*', '');
            }
            sibling = sibling.previousElementSibling;
        }

        // Fallback to placeholder or name
        return element.placeholder || element.name || 'حقل غير معروف';
    }

    // Display interactive form template
    function displayFormTemplate(formFields) {
        let html = '<div class="form-template-fields">';

        // Group fields by sections
        const sections = {
            'المعلومات الأساسية': [],
            'الأسعار': [],
            'التصنيفات والوحدات': [],
            'الحسابات المالية': [],
            'التفاصيل الإضافية': []
        };

        // Categorize fields
        formFields.forEach(field => {
            if (field.name === 'name' || field.name === 'sku') {
                sections['المعلومات الأساسية'].push(field);
            } else if (field.name === 'sale_price' || field.name === 'purchase_price') {
                sections['الأسعار'].push(field);
            } else if (field.name === 'category_id' || field.name === 'unit_id' || field.name === 'tax_id') {
                sections['التصنيفات والوحدات'].push(field);
            } else if (field.name === 'sale_chartaccount_id' || field.name === 'expense_chartaccount_id') {
                sections['الحسابات المالية'].push(field);
            } else {
                sections['التفاصيل الإضافية'].push(field);
            }
        });

        // Generate HTML for each section
        Object.entries(sections).forEach(([sectionName, sectionFields]) => {
            if (sectionFields.length > 0) {
                html += `<div class="section-divider">`;
                html += `<div class="section-title"><i class="ti ti-settings"></i>${sectionName}</div>`;

                sectionFields.forEach(field => {
                    html += createFieldHTML(field);
                });

                html += `</div>`;
            }
        });

        html += '</div>';
        templateContent.innerHTML = html;

        // Setup field interaction
        setupFieldInteraction();
    }

    // Create HTML for individual field
    function createFieldHTML(field) {
        const fieldId = `field-${field.name || field.id}`;
        const requiredMark = field.required ? '<span class="field-required">*</span>' : '';
        const typeClass = field.inputType || field.type;

        let optionsHTML = '';
        if (field.options && field.options.length > 0) {
            optionsHTML = `
                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                    الخيارات: ${field.options.map(opt => opt.text).join(', ')}
                </div>
            `;
        }

        return `
            <div class="form-field-item" data-field-name="${field.name}" data-field-type="${field.type}" id="${fieldId}">
                <div class="field-label">
                    <span class="field-type-badge ${typeClass}">${field.type}</span>
                    ${field.label} ${requiredMark}
                </div>

                <div class="field-mapping-info" id="mapping-${fieldId}">
                    <div>انقر لربط هذا الحقل بعمود من البيانات</div>
                    <select class="column-selector" id="selector-${fieldId}">
                        <option value="">اختر العمود...</option>
                    </select>
                    <div class="mapping-actions">
                        <button class="btn-map" onclick="mapField('${field.name}', '${fieldId}')">ربط</button>
                        <button class="btn-unmap" onclick="unmapField('${fieldId}')" style="display: none;">إلغاء الربط</button>
                    </div>
                </div>

                ${optionsHTML}
            </div>
        `;
    }

    // Setup field interaction
    function setupFieldInteraction() {
        // Populate column selectors with available data columns
        if (uploadedData && uploadedData.columns) {
            const selectors = document.querySelectorAll('.column-selector');
            selectors.forEach(selector => {
                uploadedData.columns.forEach(column => {
                    const option = document.createElement('option');
                    option.value = column;
                    option.textContent = column;
                    selector.appendChild(option);
                });
            });
        }

        // Add click handlers for field items
        const fieldItems = document.querySelectorAll('.form-field-item');
        fieldItems.forEach(item => {
            item.addEventListener('click', function() {
                const mappingInfo = this.querySelector('.field-mapping-info');
                if (mappingInfo.style.display === 'none') {
                    mappingInfo.style.display = 'block';
                } else {
                    mappingInfo.style.display = 'block';
                }
            });
        });
    }

    // Map field to data column
    function mapField(fieldName, fieldId) {
        const selector = document.getElementById(`selector-${fieldId}`);
        const selectedColumn = selector.value;

        if (!selectedColumn) {
            showNotification('يرجى اختيار عمود من البيانات', 'warning');
            return;
        }

        // Store mapping
        fieldMappings[fieldName] = {
            column: selectedColumn,
            fieldId: fieldId,
            fieldType: document.querySelector(`[data-field-name="${fieldName}"]`).dataset.fieldType
        };

        // Update UI
        const fieldItem = document.getElementById(fieldId);
        const mappingInfo = document.getElementById(`mapping-${fieldId}`);
        const mapBtn = mappingInfo.querySelector('.btn-map');
        const unmapBtn = mappingInfo.querySelector('.btn-unmap');

        fieldItem.classList.add('mapped');
        mappingInfo.classList.add('mapped');
        mappingInfo.innerHTML = `
            <div><strong>مربوط بالعمود:</strong> ${selectedColumn}</div>
            <div><strong>قيمة تجريبية:</strong> ${getFirstRowValue(selectedColumn)}</div>
        `;

        mapBtn.style.display = 'none';
        unmapBtn.style.display = 'inline-block';

        showNotification(`تم ربط الحقل "${fieldName}" بالعمود "${selectedColumn}"`, 'success');

        // Enable filling button if we have mappings
        updateFillingButtonState();
    }

    // Unmap field
    function unmapField(fieldId) {
        const fieldItem = document.getElementById(fieldId);
        const fieldName = fieldItem.dataset.fieldName;

        // Remove mapping
        delete fieldMappings[fieldName];

        // Update UI
        const mappingInfo = document.getElementById(`mapping-${fieldId}`);
        fieldItem.classList.remove('mapped');
        mappingInfo.classList.remove('mapped');

        // Restore original mapping interface
        const selector = document.getElementById(`selector-${fieldId}`);
        mappingInfo.innerHTML = `
            <div>انقر لربط هذا الحقل بعمود من البيانات</div>
            <select class="column-selector" id="selector-${fieldId}">
                <option value="">اختر العمود...</option>
            </select>
            <div class="mapping-actions">
                <button class="btn-map" onclick="mapField('${fieldName}', '${fieldId}')">ربط</button>
                <button class="btn-unmap" onclick="unmapField('${fieldId}')" style="display: none;">إلغاء الربط</button>
            </div>
        `;

        // Re-populate selector
        if (uploadedData && uploadedData.columns) {
            const newSelector = document.getElementById(`selector-${fieldId}`);
            uploadedData.columns.forEach(column => {
                const option = document.createElement('option');
                option.value = column;
                option.textContent = column;
                newSelector.appendChild(option);
            });
        }

        showNotification(`تم إلغاء ربط الحقل "${fieldName}"`, 'info');
        updateFillingButtonState();
    }

    // Get first row value for preview
    function getFirstRowValue(column) {
        if (uploadedData && uploadedData.data && uploadedData.data.length > 0) {
            return uploadedData.data[0][column] || 'فارغ';
        }
        return 'غير متاح';
    }

    // Update filling button state
    function updateFillingButtonState() {
        const mappingCount = Object.keys(fieldMappings).length;
        startFillingBtn.disabled = mappingCount === 0 || selectedRows.size === 0;

        if (mappingCount > 0 && selectedRows.size > 0) {
            startFillingBtn.textContent = `🚀 تعبئة ${selectedRows.size} صف`;
        } else {
            startFillingBtn.textContent = '🚀 بدء التعبئة';
        }
    }

    // Create hardcoded template as fallback
    function createHardcodedTemplate() {
        const hardcodedFields = [
            { type: 'input', inputType: 'text', name: 'name', label: 'اسم المنتج', required: true },
            { type: 'input', inputType: 'text', name: 'sku', label: 'رمز المنتج', required: false },
            { type: 'input', inputType: 'number', name: 'sale_price', label: 'سعر البيع', required: true },
            { type: 'input', inputType: 'number', name: 'purchase_price', label: 'سعر الشراء', required: false },
            { type: 'select', name: 'category_id', label: 'التصنيف', required: true, options: [
                { value: '1', text: 'إلكترونيات' },
                { value: '2', text: 'ملابس' },
                { value: '3', text: 'أطعمة' }
            ]},
            { type: 'select', name: 'unit_id', label: 'الوحدة', required: true, options: [
                { value: '1', text: 'قطعة' },
                { value: '2', text: 'كيلو' },
                { value: '3', text: 'متر' }
            ]},
            { type: 'textarea', name: 'description', label: 'الوصف', required: false }
        ];

        displayFormTemplate(hardcodedFields);
    }

    // Toggle template visibility
    function toggleTemplate() {
        const content = templateContent;
        if (content.style.display === 'none') {
            content.style.display = 'block';
            toggleTemplateBtn.textContent = 'إخفاء';
        } else {
            content.style.display = 'none';
            toggleTemplateBtn.textContent = 'إظهار';
        }
    }

    // Save mapping configuration
    function saveMapping() {
        if (Object.keys(fieldMappings).length === 0) {
            showNotification('لا توجد تعيينات لحفظها', 'warning');
            return;
        }

        const mappingConfig = {
            url: websiteUrl.value,
            mappings: fieldMappings,
            timestamp: Date.now(),
            name: `تعيين ${new Date().toLocaleDateString('ar-SA')}`
        };

        // Save to chrome storage
        chrome.storage.local.get(['savedMappings'], function(result) {
            const savedMappings = result.savedMappings || [];
            savedMappings.push(mappingConfig);

            chrome.storage.local.set({ savedMappings: savedMappings }, function() {
                showNotification('تم حفظ التعيين بنجاح', 'success');
            });
        });
    }

    function detectFields() {
        analyzePageStructure();
    }

    // Alternative analysis method using content script injection
    function analyzePageWithContentScript() {
        // Get the current tab ID of the iframe
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                // Inject content script to analyze the page
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    function: analyzePageFromContentScript
                }, function(results) {
                    if (chrome.runtime.lastError) {
                        console.error('Content script injection failed:', chrome.runtime.lastError);
                        showNotification('فشل في تحليل الصفحة - جرب صفحة أخرى', 'error');
                        // Fallback to local file analysis
                        tryLocalFileAnalysis();
                    } else if (results && results[0] && results[0].result) {
                        const pageStructure = results[0].result;
                        displayPageAnalysis(pageStructure);
                        showNotification('تم تحليل هيكل الصفحة بنجاح', 'success');

                        // Store the analysis
                        chrome.storage.local.set({ pageStructure: pageStructure });
                    } else {
                        showNotification('لم يتم العثور على عناصر في الصفحة', 'warning');
                    }
                });
            } else {
                tryLocalFileAnalysis();
            }
        });
    }

    // Function to be injected into the page for analysis
    function analyzePageFromContentScript() {
        const pageStructure = {
            url: window.location.href,
            title: document.title,
            forms: [],
            buttons: [],
            inputs: [],
            selects: [],
            textareas: [],
            links: [],
            images: [],
            timestamp: Date.now()
        };

        // Analyze forms
        const forms = document.querySelectorAll('form');
        forms.forEach((form, index) => {
            const formInfo = {
                index: index,
                id: form.id || '',
                name: form.name || '',
                action: form.action || '',
                method: form.method || 'GET',
                fields: [],
                selector: generateSelectorForElement(form)
            };

            // Analyze form fields
            const fields = form.querySelectorAll('input, select, textarea');
            fields.forEach((field, fieldIndex) => {
                const fieldInfo = {
                    index: fieldIndex,
                    tagName: field.tagName.toLowerCase(),
                    type: field.type || '',
                    name: field.name || '',
                    id: field.id || '',
                    placeholder: field.placeholder || '',
                    value: field.value || '',
                    required: field.required || false,
                    disabled: field.disabled || false,
                    readonly: field.readOnly || false,
                    label: findLabelForField(field),
                    selector: generateSelectorForElement(field)
                };

                // Special handling for select elements
                if (field.tagName.toLowerCase() === 'select') {
                    fieldInfo.options = Array.from(field.options).map(option => ({
                        value: option.value,
                        text: option.text,
                        selected: option.selected
                    }));
                    fieldInfo.multiple = field.multiple;
                }

                formInfo.fields.push(fieldInfo);

                // Add to global arrays
                if (field.tagName.toLowerCase() === 'input') {
                    pageStructure.inputs.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'select') {
                    pageStructure.selects.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'textarea') {
                    pageStructure.textareas.push(fieldInfo);
                }
            });

            pageStructure.forms.push(formInfo);
        });

        // Analyze buttons
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], input[type="reset"]');
        buttons.forEach((button, index) => {
            const buttonInfo = {
                index: index,
                tagName: button.tagName.toLowerCase(),
                type: button.type || '',
                text: button.textContent.trim() || button.value || '',
                name: button.name || '',
                id: button.id || '',
                disabled: button.disabled || false,
                selector: generateSelectorForElement(button)
            };
            pageStructure.buttons.push(buttonInfo);
        });

        // Analyze links (limit to first 20)
        const links = document.querySelectorAll('a[href]');
        Array.from(links).slice(0, 20).forEach((link, index) => {
            pageStructure.links.push({
                index: index,
                text: link.textContent.trim(),
                href: link.href,
                target: link.target || '',
                selector: generateSelectorForElement(link)
            });
        });

        // Analyze images (limit to first 10)
        const images = document.querySelectorAll('img');
        Array.from(images).slice(0, 10).forEach((img, index) => {
            pageStructure.images.push({
                index: index,
                src: img.src,
                alt: img.alt || '',
                width: img.width || 0,
                height: img.height || 0,
                selector: generateSelectorForElement(img)
            });
        });

        // Helper functions for content script
        function generateSelectorForElement(element) {
            if (element.id) {
                return `#${element.id}`;
            }
            if (element.name) {
                return `[name="${element.name}"]`;
            }
            if (element.className) {
                const classes = element.className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    return `.${classes.join('.')}`;
                }
            }

            // Generate path-based selector
            const path = [];
            let current = element;
            while (current && current !== document.body && path.length < 4) {
                let selector = current.tagName.toLowerCase();
                const siblings = Array.from(current.parentElement?.children || [])
                    .filter(sibling => sibling.tagName === current.tagName);
                if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += `:nth-of-type(${index})`;
                }
                path.unshift(selector);
                current = current.parentElement;
            }
            return path.join(' > ');
        }

        function findLabelForField(field) {
            if (field.id) {
                const label = document.querySelector(`label[for="${field.id}"]`);
                if (label) return label.textContent.trim();
            }

            let parent = field.parentElement;
            while (parent && parent !== document.body) {
                if (parent.tagName.toLowerCase() === 'label') {
                    return parent.textContent.trim();
                }
                parent = parent.parentElement;
            }

            let sibling = field.previousElementSibling;
            while (sibling) {
                if (sibling.tagName.toLowerCase() === 'label') {
                    return sibling.textContent.trim();
                }
                sibling = sibling.previousElementSibling;
            }

            return field.placeholder || field.name || '';
        }

        return pageStructure;
    }

    // Fallback for local file analysis
    function tryLocalFileAnalysis() {
        showNotification('جاري المحاولة بطريقة محلية...', 'info');

        // Create a simple analysis for demonstration
        const demoStructure = {
            url: websiteFrame.src,
            title: 'تحليل تجريبي',
            forms: [{
                index: 0,
                id: 'demo-form',
                name: 'demoForm',
                action: '/submit',
                method: 'POST',
                fields: [
                    {
                        index: 0,
                        tagName: 'input',
                        type: 'text',
                        name: 'name',
                        label: 'الاسم',
                        required: true
                    },
                    {
                        index: 1,
                        tagName: 'input',
                        type: 'email',
                        name: 'email',
                        label: 'البريد الإلكتروني',
                        required: true
                    }
                ]
            }],
            buttons: [{
                index: 0,
                tagName: 'button',
                type: 'submit',
                text: 'إرسال'
            }],
            inputs: [],
            selects: [],
            textareas: [],
            links: [],
            images: [],
            timestamp: Date.now()
        };

        displayPageAnalysis(demoStructure);
        showNotification('تم إنشاء تحليل تجريبي - لاختبار كامل استخدم صفحة محلية', 'warning');
    }

    // Analyze local pages (works with file:// URLs)
    function analyzeLocalPage() {
        try {
            const iframeDoc = websiteFrame.contentDocument || websiteFrame.contentWindow.document;
            if (iframeDoc) {
                // Direct analysis for local files
                const pageStructure = analyzeDocumentStructure(iframeDoc);
                displayPageAnalysis(pageStructure);
                highlightDetectedElements(iframeDoc, pageStructure);
                showNotification('تم تحليل الصفحة المحلية بنجاح', 'success');

                // Store the analysis
                chrome.storage.local.set({ pageStructure: pageStructure });
            } else {
                showNotification('لا يمكن الوصول إلى الصفحة المحلية', 'error');
            }
        } catch (error) {
            console.error('Error analyzing local page:', error);
            showNotification('خطأ في تحليل الصفحة المحلية: ' + error.message, 'error');
        }
    }

    // Direct document analysis function
    function analyzeDocumentStructure(doc) {
        const pageStructure = {
            url: doc.location ? doc.location.href : 'local-file',
            title: doc.title || 'صفحة محلية',
            forms: [],
            buttons: [],
            inputs: [],
            selects: [],
            textareas: [],
            links: [],
            images: [],
            timestamp: Date.now()
        };

        // Analyze forms
        const forms = doc.querySelectorAll('form');
        forms.forEach((form, index) => {
            const formInfo = {
                index: index,
                id: form.id || '',
                name: form.name || '',
                action: form.action || '',
                method: form.method || 'GET',
                fields: [],
                selector: generateSelector(form, doc)
            };

            // Analyze form fields
            const fields = form.querySelectorAll('input, select, textarea');
            fields.forEach((field, fieldIndex) => {
                const fieldInfo = analyzeFormField(field, fieldIndex, doc);
                formInfo.fields.push(fieldInfo);

                // Add to global arrays
                if (field.tagName.toLowerCase() === 'input') {
                    pageStructure.inputs.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'select') {
                    pageStructure.selects.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'textarea') {
                    pageStructure.textareas.push(fieldInfo);
                }
            });

            pageStructure.forms.push(formInfo);
        });

        // Analyze buttons
        const buttons = doc.querySelectorAll('button, input[type="button"], input[type="submit"], input[type="reset"]');
        buttons.forEach((button, index) => {
            const buttonInfo = analyzeButton(button, index, doc);
            pageStructure.buttons.push(buttonInfo);
        });

        // Analyze links (limit to first 20)
        const links = doc.querySelectorAll('a[href]');
        Array.from(links).slice(0, 20).forEach((link, index) => {
            pageStructure.links.push({
                index: index,
                text: link.textContent.trim(),
                href: link.href,
                target: link.target || '',
                selector: generateSelector(link, doc)
            });
        });

        // Analyze images (limit to first 10)
        const images = doc.querySelectorAll('img');
        Array.from(images).slice(0, 10).forEach((img, index) => {
            pageStructure.images.push({
                index: index,
                src: img.src,
                alt: img.alt || '',
                width: img.width || 0,
                height: img.height || 0,
                selector: generateSelector(img, doc)
            });
        });

        return pageStructure;
    }

    function displayPageAnalysis(structure) {
        // Create analysis panel
        let analysisPanel = document.getElementById('analysis-panel');
        if (!analysisPanel) {
            analysisPanel = document.createElement('div');
            analysisPanel.id = 'analysis-panel';
            analysisPanel.className = 'analysis-panel';
            analysisPanel.innerHTML = `
                <div class="analysis-header">
                    <h3>📊 تحليل هيكل الصفحة</h3>
                    <button class="btn btn-small" id="toggle-analysis">إخفاء/إظهار</button>
                </div>
                <div class="analysis-content" id="analysis-content"></div>
            `;

            // Insert after stats bar
            const statsBar = document.querySelector('.stats-bar');
            statsBar.parentNode.insertBefore(analysisPanel, statsBar.nextSibling);

            // Setup toggle functionality
            document.getElementById('toggle-analysis').addEventListener('click', function() {
                const content = document.getElementById('analysis-content');
                content.style.display = content.style.display === 'none' ? 'block' : 'none';
            });
        }

        const analysisContent = document.getElementById('analysis-content');
        analysisContent.innerHTML = `
            <div class="analysis-summary">
                <div class="summary-item">
                    <span class="summary-icon">📝</span>
                    <span class="summary-label">النماذج:</span>
                    <span class="summary-value">${structure.forms.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📋</span>
                    <span class="summary-label">حقول الإدخال:</span>
                    <span class="summary-value">${structure.inputs.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📋</span>
                    <span class="summary-label">القوائم المنسدلة:</span>
                    <span class="summary-value">${structure.selects.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">🔘</span>
                    <span class="summary-label">الأزرار:</span>
                    <span class="summary-value">${structure.buttons.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">🔗</span>
                    <span class="summary-label">الروابط:</span>
                    <span class="summary-value">${structure.links.length}</span>
                </div>
            </div>

            <div class="analysis-details">
                ${generateFormsAnalysis(structure.forms)}
                ${generateFieldsAnalysis(structure.inputs, 'حقول الإدخال', '📋')}
                ${generateFieldsAnalysis(structure.selects, 'القوائم المنسدلة', '📋')}
                ${generateButtonsAnalysis(structure.buttons)}
            </div>
        `;
    }

    function generateFormsAnalysis(forms) {
        if (forms.length === 0) return '';

        return `
            <div class="analysis-section">
                <h4>📝 النماذج المكتشفة (${forms.length})</h4>
                ${forms.map((form, index) => `
                    <div class="form-item">
                        <div class="form-header">
                            <strong>نموذج ${index + 1}</strong>
                            ${form.id ? `<span class="form-id">#${form.id}</span>` : ''}
                        </div>
                        <div class="form-details">
                            <div>الإجراء: ${form.action || 'غير محدد'}</div>
                            <div>الطريقة: ${form.method}</div>
                            <div>عدد الحقول: ${form.fields.length}</div>
                        </div>
                        <div class="form-fields">
                            ${form.fields.map(field => `
                                <div class="field-item">
                                    <span class="field-type">${field.type || field.tagName}</span>
                                    <span class="field-name">${field.label || field.name || field.placeholder || 'بدون اسم'}</span>
                                    ${field.required ? '<span class="field-required">مطلوب</span>' : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    function generateFieldsAnalysis(fields, title, icon) {
        if (fields.length === 0) return '';

        return `
            <div class="analysis-section">
                <h4>${icon} ${title} (${fields.length})</h4>
                <div class="fields-grid">
                    ${fields.map(field => `
                        <div class="field-card">
                            <div class="field-card-header">
                                <span class="field-type-badge">${field.type || field.tagName}</span>
                                ${field.required ? '<span class="required-badge">مطلوب</span>' : ''}
                            </div>
                            <div class="field-card-content">
                                <div class="field-label">${field.label || field.name || field.placeholder || 'بدون تسمية'}</div>
                                ${field.options ? `<div class="field-options">خيارات: ${field.options.length}</div>` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function generateButtonsAnalysis(buttons) {
        if (buttons.length === 0) return '';

        return `
            <div class="analysis-section">
                <h4>🔘 الأزرار المكتشفة (${buttons.length})</h4>
                <div class="buttons-grid">
                    ${buttons.map(button => `
                        <div class="button-card">
                            <div class="button-type">${button.type || button.tagName}</div>
                            <div class="button-text">${button.text || 'بدون نص'}</div>
                            ${button.disabled ? '<span class="disabled-badge">معطل</span>' : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function highlightDetectedElements(doc, structure) {
        try {
            // Remove existing highlights
            const existingHighlights = doc.querySelectorAll('.smart-highlight');
            existingHighlights.forEach(el => el.classList.remove('smart-highlight'));

            // Highlight forms
            structure.forms.forEach(form => {
                const formElement = doc.querySelector(form.selector);
                if (formElement) {
                    formElement.classList.add('smart-highlight', 'highlight-form');
                }
            });

            // Highlight input fields
            structure.inputs.forEach(input => {
                const inputElement = doc.querySelector(input.selector);
                if (inputElement) {
                    inputElement.classList.add('smart-highlight', 'highlight-input');
                }
            });

            // Highlight select fields
            structure.selects.forEach(select => {
                const selectElement = doc.querySelector(select.selector);
                if (selectElement) {
                    selectElement.classList.add('smart-highlight', 'highlight-select');
                }
            });

            // Highlight buttons
            structure.buttons.forEach(button => {
                const buttonElement = doc.querySelector(button.selector);
                if (buttonElement) {
                    buttonElement.classList.add('smart-highlight', 'highlight-button');
                }
            });

        } catch (error) {
            console.error('Error highlighting elements:', error);
        }
    }

    function startFilling() {
        if (selectedRows.size === 0) {
            showNotification('يرجى تحديد صفوف للتعبئة', 'warning');
            return;
        }

        if (Object.keys(fieldMappings).length === 0) {
            showNotification('يرجى ربط الحقول بالبيانات أولاً', 'warning');
            return;
        }

        const targetUrl = websiteUrl.value.trim();
        if (!targetUrl) {
            showNotification('يرجى إدخال رابط الموقع المستهدف', 'warning');
            return;
        }

        // Start the filling process
        showNotification('بدء عملية التعبئة التلقائية...', 'info');

        // Convert selected rows to array
        const rowsToFill = Array.from(selectedRows);
        let currentRowIndex = 0;

        // Process each row
        processNextRow(rowsToFill, currentRowIndex, targetUrl);
    }

    // Process individual row
    function processNextRow(rowsToFill, currentRowIndex, targetUrl) {
        if (currentRowIndex >= rowsToFill.length) {
            showNotification('تم الانتهاء من تعبئة جميع الصفوف!', 'success');
            return;
        }

        const rowIndex = rowsToFill[currentRowIndex];
        const rowData = uploadedData.data[rowIndex];

        showNotification(`جاري تعبئة الصف ${currentRowIndex + 1} من ${rowsToFill.length}...`, 'info');

        // Open new tab for filling
        chrome.tabs.create({ url: targetUrl }, function(tab) {
            // Wait for page to load then fill
            setTimeout(() => {
                fillFormInTab(tab.id, rowData, () => {
                    // Mark row as completed
                    markRowAsCompleted(rowIndex);

                    // Process next row after delay
                    setTimeout(() => {
                        processNextRow(rowsToFill, currentRowIndex + 1, targetUrl);
                    }, 2000);
                });
            }, 3000);
        });
    }

    // Fill form in specific tab
    function fillFormInTab(tabId, rowData, callback) {
        // Prepare filling data
        const fillingData = [];

        Object.entries(fieldMappings).forEach(([fieldName, mapping]) => {
            const value = rowData[mapping.column] || '';
            fillingData.push({
                fieldName: fieldName,
                value: value,
                type: mapping.fieldType
            });
        });

        // Inject content script to fill the form
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: fillFormOnPage,
            args: [fillingData]
        }, function(results) {
            if (chrome.runtime.lastError) {
                console.error('Error filling form:', chrome.runtime.lastError);
                showNotification('خطأ في تعبئة النموذج', 'error');
            } else {
                console.log('Form filled successfully');

                // Submit the form
                setTimeout(() => {
                    submitFormInTab(tabId, callback);
                }, 1000);
            }
        });
    }

    // Submit form in tab
    function submitFormInTab(tabId, callback) {
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: submitFormOnPage
        }, function(results) {
            if (chrome.runtime.lastError) {
                console.error('Error submitting form:', chrome.runtime.lastError);
            }

            // Close tab after submission
            setTimeout(() => {
                chrome.tabs.remove(tabId);
                if (callback) callback();
            }, 2000);
        });
    }

    // Function to be injected for form filling
    function fillFormOnPage(fillingData) {
        console.log('Filling form with data:', fillingData);

        fillingData.forEach(item => {
            // Try different selectors to find the field
            let field = null;

            // Try by name
            field = document.querySelector(`[name="${item.fieldName}"]`);

            // Try by id
            if (!field) {
                field = document.querySelector(`#${item.fieldName}`);
            }

            // Try by data attribute
            if (!field) {
                field = document.querySelector(`[data-name="${item.fieldName}"]`);
            }

            if (field) {
                if (field.tagName.toLowerCase() === 'select') {
                    // Handle select fields
                    const option = Array.from(field.options).find(opt =>
                        opt.text.includes(item.value) || opt.value === item.value
                    );
                    if (option) {
                        field.value = option.value;
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                } else if (field.type === 'radio') {
                    // Handle radio buttons
                    const radioOption = document.querySelector(`[name="${item.fieldName}"][value="${item.value}"]`);
                    if (radioOption) {
                        radioOption.checked = true;
                        radioOption.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                } else {
                    // Handle regular input fields
                    field.value = item.value;
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                }

                console.log(`Filled field ${item.fieldName} with value: ${item.value}`);
            } else {
                console.warn(`Field not found: ${item.fieldName}`);
            }
        });

        return { success: true, fieldsProcessed: fillingData.length };
    }

    // Function to be injected for form submission
    function submitFormOnPage() {
        // Look for submit button
        let submitBtn = document.querySelector('input[type="submit"][value*="إنشاء"]');

        if (!submitBtn) {
            submitBtn = document.querySelector('button[type="submit"]');
        }

        if (!submitBtn) {
            submitBtn = document.querySelector('.btn-primary');
        }

        if (submitBtn) {
            console.log('Submitting form...');
            submitBtn.click();
            return { success: true, submitted: true };
        } else {
            console.warn('Submit button not found');
            return { success: false, error: 'Submit button not found' };
        }
    }

    // Mark row as completed in the data table
    function markRowAsCompleted(rowIndex) {
        const row = document.querySelector(`tr[data-row-index="${rowIndex}"]`);
        if (row) {
            row.classList.add('completed');
            row.style.backgroundColor = '#d4edda';
            row.style.opacity = '0.7';

            // Add checkmark
            const firstCell = row.querySelector('td');
            if (firstCell && !firstCell.querySelector('.completed-mark')) {
                const checkmark = document.createElement('span');
                checkmark.className = 'completed-mark';
                checkmark.innerHTML = ' ✅';
                checkmark.style.color = '#28a745';
                checkmark.style.fontWeight = 'bold';
                firstCell.appendChild(checkmark);
            }
        }
    }

    function copySelectedData() {
        if (selectedRows.size === 0) {
            showNotification('يرجى تحديد صفوف للنسخ', 'warning');
            return;
        }
        showNotification('ميزة نسخ البيانات قيد التطوير', 'info');
    }

    function mapColumnToField(column) {
        showNotification(`تم تعيين العمود: ${column}`, 'success');
        fieldMappings[column] = { mapped: true, timestamp: Date.now() };
    }

    function showCustomValueInput() {
        showNotification('ميزة القيمة المخصصة قيد التطوير', 'info');
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ القيمة', 'success');
        });
    }

    // Make functions globally available
    window.mapField = mapField;
    window.unmapField = unmapField;

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideDown 0.3s ease-out;
            max-width: 400px;
            text-align: center;
            font-family: inherit;
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.background = '#28a745';
                break;
            case 'error':
                notification.style.background = '#dc3545';
                break;
            case 'warning':
                notification.style.background = '#ffc107';
                notification.style.color = '#212529';
                break;
            default:
                notification.style.background = '#17a2b8';
        }

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideUp 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }
});
