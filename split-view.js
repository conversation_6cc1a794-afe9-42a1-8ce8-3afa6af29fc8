// Smart Form Filler - Split View Script
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const dataTableContainer = document.getElementById('data-table-container');
    const websiteFrame = document.getElementById('website-frame');
    const websiteUrl = document.getElementById('website-url');
    const loadWebsiteBtn = document.getElementById('load-website');
    const refreshWebsiteBtn = document.getElementById('refresh-website');
    const autoSetupBtn = document.getElementById('auto-setup');
    const detectFieldsBtn = document.getElementById('detect-fields');
    const startFillingBtn = document.getElementById('start-filling');
    const copyDataBtn = document.getElementById('copy-data');
    const resizeHandle = document.getElementById('resize-handle');
    
    // Stats elements
    const totalRowsElement = document.getElementById('total-rows');
    const totalColumnsElement = document.getElementById('total-columns');
    const selectedRowsElement = document.getElementById('selected-rows');

    // State variables
    let uploadedData = null;
    let selectedRows = new Set();
    let fieldMappings = {};
    let isResizing = false;
    let savedUrl = '';

    // Initialize
    init();

    function init() {
        // Load uploaded data from storage
        loadUploadedData();
        
        // Setup event listeners
        setupEventListeners();
        
        // Load saved URL
        loadSavedUrl();
    }

    function setupEventListeners() {
        loadWebsiteBtn.addEventListener('click', loadWebsite);
        refreshWebsiteBtn.addEventListener('click', refreshWebsite);
        autoSetupBtn.addEventListener('click', showAutoSetup);
        detectFieldsBtn.addEventListener('click', detectFields);
        startFillingBtn.addEventListener('click', startFilling);
        copyDataBtn.addEventListener('click', copySelectedData);
        
        // URL input events
        websiteUrl.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadWebsite();
            }
        });
        
        websiteUrl.addEventListener('input', saveUrl);
        
        // Resize functionality
        setupResizeHandle();
    }

    function loadUploadedData() {
        chrome.storage.local.get(['uploadedData'], function(result) {
            if (result.uploadedData) {
                uploadedData = result.uploadedData;
                displayDataTable();
                updateStats();
            } else {
                showNoDataMessage();
            }
        });
    }

    function displayDataTable() {
        if (!uploadedData || !uploadedData.data || uploadedData.data.length === 0) {
            showNoDataMessage();
            return;
        }

        const data = uploadedData.data;
        const columns = uploadedData.columns;

        // Create table
        const table = document.createElement('table');
        table.className = 'data-table';
        table.id = 'data-viewer-table';

        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // Add checkbox column
        const checkboxHeader = document.createElement('th');
        checkboxHeader.innerHTML = '<input type="checkbox" id="select-all" class="row-selector">';
        headerRow.appendChild(checkboxHeader);

        // Add data columns
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Create body
        const tbody = document.createElement('tbody');
        
        data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.dataset.rowIndex = index;

            // Add checkbox cell
            const checkboxCell = document.createElement('td');
            checkboxCell.innerHTML = `<input type="checkbox" class="row-selector" data-row="${index}">`;
            tr.appendChild(checkboxCell);

            // Add data cells
            columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                td.dataset.column = column;
                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });

        table.appendChild(tbody);

        // Clear container and add table
        dataTableContainer.innerHTML = '';
        dataTableContainer.appendChild(table);

        // Setup table event listeners
        setupTableEventListeners();
    }

    function setupTableEventListeners() {
        const selectAllCheckbox = document.getElementById('select-all');
        const rowCheckboxes = document.querySelectorAll('.row-selector[data-row]');

        // Select all functionality
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const rowIndex = parseInt(checkbox.dataset.row);
                if (isChecked) {
                    selectedRows.add(rowIndex);
                    checkbox.closest('tr').classList.add('selected');
                } else {
                    selectedRows.delete(rowIndex);
                    checkbox.closest('tr').classList.remove('selected');
                }
            });
            updateSelectedCount();
        });

        // Individual row selection
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const rowIndex = parseInt(this.dataset.row);
                const row = this.closest('tr');
                
                if (this.checked) {
                    selectedRows.add(rowIndex);
                    row.classList.add('selected');
                } else {
                    selectedRows.delete(rowIndex);
                    row.classList.remove('selected');
                }
                
                updateSelectedCount();
                
                // Update select all checkbox
                selectAllCheckbox.checked = selectedRows.size === rowCheckboxes.length;
            });
        });

        // Right-click context menu for field mapping
        const dataCells = document.querySelectorAll('.data-table td[data-column]');
        dataCells.forEach(cell => {
            cell.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                showColumnContextMenu(e, this.dataset.column, this.textContent);
            });
        });
    }

    function showColumnContextMenu(event, columnName, cellValue) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Create context menu
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.innerHTML = `
            <div class="context-menu-header">تعيين العمود: ${columnName}</div>
            <div class="context-menu-item" data-action="map-column" data-column="${columnName}">
                <span class="column-icon">📋</span>
                تعيين هذا العمود للحقل المحدد
            </div>
            <div class="context-menu-item custom-value" data-action="custom-value">
                <span class="custom-icon">✏️</span>
                استخدام قيمة مخصصة
            </div>
            <div class="context-menu-item" data-action="copy-value">
                <span class="column-icon">📄</span>
                نسخ القيمة: ${cellValue}
            </div>
        `;

        // Position menu
        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';

        // Add to page
        document.body.appendChild(menu);

        // Add event listeners
        menu.addEventListener('click', function(e) {
            const action = e.target.closest('.context-menu-item')?.dataset.action;
            const column = e.target.closest('.context-menu-item')?.dataset.column;
            
            if (action === 'map-column') {
                mapColumnToField(column);
            } else if (action === 'custom-value') {
                showCustomValueInput();
            } else if (action === 'copy-value') {
                copyToClipboard(cellValue);
            }
            
            menu.remove();
        });

        // Remove menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function removeMenu() {
                menu.remove();
                document.removeEventListener('click', removeMenu);
            });
        }, 100);
    }

    function loadWebsite() {
        const url = websiteUrl.value.trim();
        if (!url) {
            showNotification('يرجى إدخال رابط الموقع', 'warning');
            return;
        }

        // Add protocol if missing
        let formattedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            formattedUrl = 'https://' + url;
        }

        websiteFrame.src = formattedUrl;
        saveUrl();
        showNotification('جاري تحميل الموقع...', 'info');
    }

    function refreshWebsite() {
        if (savedUrl) {
            websiteUrl.value = savedUrl;
            websiteFrame.src = savedUrl;
            showNotification('تم تحديث الصفحة', 'success');
        } else {
            loadWebsite();
        }
    }

    function saveUrl() {
        savedUrl = websiteUrl.value.trim();
        chrome.storage.local.set({ savedUrl: savedUrl });
    }

    function loadSavedUrl() {
        chrome.storage.local.get(['savedUrl'], function(result) {
            if (result.savedUrl) {
                savedUrl = result.savedUrl;
                websiteUrl.value = savedUrl;
            }
        });
    }

    function showNoDataMessage() {
        dataTableContainer.innerHTML = `
            <div class="no-data">
                <p>لا توجد بيانات محملة</p>
                <p>يرجى العودة إلى النافذة الرئيسية وتحميل ملف البيانات</p>
            </div>
        `;
    }

    function updateStats() {
        if (uploadedData) {
            totalRowsElement.textContent = uploadedData.rowCount || 0;
            totalColumnsElement.textContent = uploadedData.columns?.length || 0;
        }
    }

    function updateSelectedCount() {
        selectedRowsElement.textContent = selectedRows.size;
        startFillingBtn.disabled = selectedRows.size === 0;
    }

    function setupResizeHandle() {
        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            
            const containerWidth = window.innerWidth;
            const newDataPanelWidth = (e.clientX / containerWidth) * 100;
            
            if (newDataPanelWidth >= 20 && newDataPanelWidth <= 80) {
                document.querySelector('.data-panel').style.width = newDataPanelWidth + '%';
                document.querySelector('.website-panel').style.width = (100 - newDataPanelWidth) + '%';
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    // Placeholder functions for future implementation
    function showAutoSetup() {
        showNotification('ميزة التعيين التلقائي قيد التطوير', 'info');
    }

    function detectFields() {
        showNotification('ميزة كشف الحقول قيد التطوير', 'info');
    }

    function startFilling() {
        if (selectedRows.size === 0) {
            showNotification('يرجى تحديد صفوف للتعبئة', 'warning');
            return;
        }
        showNotification('ميزة التعبئة التلقائية قيد التطوير', 'info');
    }

    function copySelectedData() {
        if (selectedRows.size === 0) {
            showNotification('يرجى تحديد صفوف للنسخ', 'warning');
            return;
        }
        showNotification('ميزة نسخ البيانات قيد التطوير', 'info');
    }

    function mapColumnToField(column) {
        showNotification(`تم تعيين العمود: ${column}`, 'success');
    }

    function showCustomValueInput() {
        showNotification('ميزة القيمة المخصصة قيد التطوير', 'info');
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ القيمة', 'success');
        });
    }

    function showNotification(message, type = 'info') {
        UIHelper.showNotification(message, type);
    }
});
