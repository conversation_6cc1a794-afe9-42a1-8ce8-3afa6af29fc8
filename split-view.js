// Smart Form Filler - Split View Script
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const dataTableContainer = document.getElementById('data-table-container');
    const websiteFrame = document.getElementById('website-frame');
    const websiteUrl = document.getElementById('website-url');
    const loadWebsiteBtn = document.getElementById('load-website');
    const refreshWebsiteBtn = document.getElementById('refresh-website');

    const resizeHandle = document.getElementById('resize-handle');
    
    // Stats elements
    const totalRowsElement = document.getElementById('total-rows');
    const totalColumnsElement = document.getElementById('total-columns');
    const selectedRowsElement = document.getElementById('selected-rows');

    // State variables
    let uploadedData = null;
    let selectedRows = new Set();
    let fieldMappings = {};
    let isResizing = false;
    let savedUrl = '';

    // Initialize
    init();

    function init() {
        // Load uploaded data from storage
        loadUploadedData();
        
        // Setup event listeners
        setupEventListeners();
        
        // Load saved URL
        loadSavedUrl();
    }

    function setupEventListeners() {
        loadWebsiteBtn.addEventListener('click', loadWebsite);
        refreshWebsiteBtn.addEventListener('click', refreshWebsite);
        
        // URL input events
        websiteUrl.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadWebsite();
            }
        });
        
        websiteUrl.addEventListener('input', saveUrl);
        
        // Resize functionality
        setupResizeHandle();
    }

    function loadUploadedData() {
        chrome.storage.local.get(['uploadedData'], function(result) {
            if (result.uploadedData) {
                uploadedData = result.uploadedData;
                displayDataTable();
                updateStats();
            } else {
                showNoDataMessage();
            }
        });
    }

    function displayDataTable() {
        if (!uploadedData || !uploadedData.data || uploadedData.data.length === 0) {
            showNoDataMessage();
            return;
        }

        const data = uploadedData.data;
        const columns = uploadedData.columns;

        // Create table
        const table = document.createElement('table');
        table.className = 'data-table';
        table.id = 'data-viewer-table';

        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // Add checkbox column
        const checkboxHeader = document.createElement('th');
        checkboxHeader.innerHTML = '<input type="checkbox" id="select-all" class="row-selector">';
        headerRow.appendChild(checkboxHeader);

        // Add data columns
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Create body
        const tbody = document.createElement('tbody');
        
        data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.dataset.rowIndex = index;

            // Add checkbox cell
            const checkboxCell = document.createElement('td');
            checkboxCell.innerHTML = `<input type="checkbox" class="row-selector" data-row="${index}">`;
            tr.appendChild(checkboxCell);

            // Add data cells
            columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                td.dataset.column = column;
                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });

        table.appendChild(tbody);

        // Clear container and add table
        dataTableContainer.innerHTML = '';
        dataTableContainer.appendChild(table);

        // Setup table event listeners
        setupTableEventListeners();
    }

    function setupTableEventListeners() {
        const selectAllCheckbox = document.getElementById('select-all');
        const rowCheckboxes = document.querySelectorAll('.row-selector[data-row]');

        // Select all functionality
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const rowIndex = parseInt(checkbox.dataset.row);
                if (isChecked) {
                    selectedRows.add(rowIndex);
                    checkbox.closest('tr').classList.add('selected');
                } else {
                    selectedRows.delete(rowIndex);
                    checkbox.closest('tr').classList.remove('selected');
                }
            });
            updateSelectedCount();
        });

        // Individual row selection
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const rowIndex = parseInt(this.dataset.row);
                const row = this.closest('tr');
                
                if (this.checked) {
                    selectedRows.add(rowIndex);
                    row.classList.add('selected');
                } else {
                    selectedRows.delete(rowIndex);
                    row.classList.remove('selected');
                }
                
                updateSelectedCount();
                
                // Update select all checkbox
                selectAllCheckbox.checked = selectedRows.size === rowCheckboxes.length;
            });
        });

        // Right-click context menu for field mapping
        const dataCells = document.querySelectorAll('.data-table td[data-column]');
        dataCells.forEach(cell => {
            cell.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                showColumnContextMenu(e, this.dataset.column, this.textContent);
            });
        });
    }

    function showColumnContextMenu(event, columnName, cellValue) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Create context menu
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.innerHTML = `
            <div class="context-menu-header">تعيين العمود: ${columnName}</div>
            <div class="context-menu-item" data-action="map-column" data-column="${columnName}">
                <span class="column-icon">📋</span>
                تعيين هذا العمود للحقل المحدد
            </div>
            <div class="context-menu-item custom-value" data-action="custom-value">
                <span class="custom-icon">✏️</span>
                استخدام قيمة مخصصة
            </div>
            <div class="context-menu-item" data-action="copy-value">
                <span class="column-icon">📄</span>
                نسخ القيمة: ${cellValue}
            </div>
        `;

        // Position menu
        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';

        // Add to page
        document.body.appendChild(menu);

        // Add event listeners
        menu.addEventListener('click', function(e) {
            const action = e.target.closest('.context-menu-item')?.dataset.action;
            const column = e.target.closest('.context-menu-item')?.dataset.column;
            
            if (action === 'map-column') {
                mapColumnToField(column);
            } else if (action === 'custom-value') {
                showCustomValueInput();
            } else if (action === 'copy-value') {
                copyToClipboard(cellValue);
            }
            
            menu.remove();
        });

        // Remove menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function removeMenu() {
                menu.remove();
                document.removeEventListener('click', removeMenu);
            });
        }, 100);
    }

    function loadWebsite() {
        const url = websiteUrl.value.trim();
        if (!url) {
            showNotification('يرجى إدخال رابط الموقع', 'warning');
            return;
        }

        // Add protocol if missing
        let formattedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            formattedUrl = 'https://' + url;
        }

        websiteFrame.src = formattedUrl;
        saveUrl();
        showNotification('جاري تحميل الموقع...', 'info');

        // Setup iframe load listener to analyze page structure
        websiteFrame.onload = function() {
            setTimeout(() => {
                // Check if it's a local file or external URL
                if (formattedUrl.startsWith('file://') || formattedUrl.includes('test-page-analysis.html')) {
                    analyzeLocalPage();
                } else {
                    analyzePageStructure();
                }
                showNotification('تم تحميل الصفحة', 'success');
            }, 1000);
        };
    }

    function refreshWebsite() {
        if (savedUrl) {
            websiteUrl.value = savedUrl;
            websiteFrame.src = savedUrl;
            showNotification('تم تحديث الصفحة', 'success');
        } else {
            loadWebsite();
        }
    }

    function saveUrl() {
        savedUrl = websiteUrl.value.trim();
        chrome.storage.local.set({ savedUrl: savedUrl });
    }

    function loadSavedUrl() {
        chrome.storage.local.get(['savedUrl'], function(result) {
            if (result.savedUrl) {
                savedUrl = result.savedUrl;
                websiteUrl.value = savedUrl;
            }
        });
    }

    function showNoDataMessage() {
        dataTableContainer.innerHTML = `
            <div class="no-data">
                <p>لا توجد بيانات محملة</p>
                <p>يرجى العودة إلى النافذة الرئيسية وتحميل ملف البيانات</p>
            </div>
        `;
    }

    function updateStats() {
        if (uploadedData) {
            totalRowsElement.textContent = uploadedData.rowCount || 0;
            totalColumnsElement.textContent = uploadedData.columns?.length || 0;
        }
    }

    function updateSelectedCount() {
        selectedRowsElement.textContent = selectedRows.size;
    }

    function setupResizeHandle() {
        resizeHandle.addEventListener('mousedown', function(_e) {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            
            const containerWidth = window.innerWidth;
            const newDataPanelWidth = (e.clientX / containerWidth) * 100;
            
            if (newDataPanelWidth >= 20 && newDataPanelWidth <= 80) {
                document.querySelector('.data-panel').style.width = newDataPanelWidth + '%';
                document.querySelector('.website-panel').style.width = (100 - newDataPanelWidth) + '%';
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    // Page structure analysis functions
    function analyzePageStructure() {
        try {
            // Try to access iframe document
            let iframeDoc = null;
            try {
                iframeDoc = websiteFrame.contentDocument || websiteFrame.contentWindow.document;
            } catch (corsError) {
                console.log('CORS error detected:', corsError.message);
                // Use content script injection instead
                analyzePageWithContentScript();
                return;
            }

            if (!iframeDoc) {
                showNotification('لا يمكن الوصول إلى محتوى الصفحة - جاري المحاولة بطريقة بديلة...', 'info');
                analyzePageWithContentScript();
                return;
            }

            const pageStructure = {
                url: websiteFrame.src,
                title: iframeDoc.title,
                forms: [],
                buttons: [],
                inputs: [],
                selects: [],
                textareas: [],
                links: [],
                images: [],
                timestamp: Date.now()
            };

            // Analyze forms
            const forms = iframeDoc.querySelectorAll('form');
            forms.forEach((form, index) => {
                const formInfo = {
                    index: index,
                    id: form.id || '',
                    name: form.name || '',
                    action: form.action || '',
                    method: form.method || 'GET',
                    fields: [],
                    selector: generateSelector(form, iframeDoc)
                };

                // Analyze form fields
                const fields = form.querySelectorAll('input, select, textarea');
                fields.forEach((field, fieldIndex) => {
                    const fieldInfo = analyzeFormField(field, fieldIndex, iframeDoc);
                    formInfo.fields.push(fieldInfo);

                    // Add to global arrays
                    if (field.tagName.toLowerCase() === 'input') {
                        pageStructure.inputs.push(fieldInfo);
                    } else if (field.tagName.toLowerCase() === 'select') {
                        pageStructure.selects.push(fieldInfo);
                    } else if (field.tagName.toLowerCase() === 'textarea') {
                        pageStructure.textareas.push(fieldInfo);
                    }
                });

                pageStructure.forms.push(formInfo);
            });

            // Analyze buttons (including those outside forms)
            const buttons = iframeDoc.querySelectorAll('button, input[type="button"], input[type="submit"], input[type="reset"]');
            buttons.forEach((button, index) => {
                const buttonInfo = analyzeButton(button, index, iframeDoc);
                pageStructure.buttons.push(buttonInfo);
            });

            // Analyze links
            const links = iframeDoc.querySelectorAll('a[href]');
            links.forEach((link, index) => {
                if (index < 20) { // Limit to first 20 links
                    pageStructure.links.push({
                        index: index,
                        text: link.textContent.trim(),
                        href: link.href,
                        target: link.target || '',
                        selector: generateSelector(link, iframeDoc)
                    });
                }
            });

            // Analyze images
            const images = iframeDoc.querySelectorAll('img');
            images.forEach((img, index) => {
                if (index < 10) { // Limit to first 10 images
                    pageStructure.images.push({
                        index: index,
                        src: img.src,
                        alt: img.alt || '',
                        width: img.width || 0,
                        height: img.height || 0,
                        selector: generateSelector(img, iframeDoc)
                    });
                }
            });

            // Store the analysis
            chrome.storage.local.set({
                pageStructure: pageStructure
            });

            // Display analysis results
            displayPageAnalysis(pageStructure);

            // Highlight detected elements
            highlightDetectedElements(iframeDoc, pageStructure);

            console.log('Page structure analyzed:', pageStructure);

        } catch (error) {
            console.error('Error analyzing page structure:', error);
            showNotification('خطأ في تحليل هيكل الصفحة: ' + error.message, 'error');
        }
    }

    function analyzeFormField(field, index, doc) {
        const fieldInfo = {
            index: index,
            tagName: field.tagName.toLowerCase(),
            type: field.type || '',
            name: field.name || '',
            id: field.id || '',
            placeholder: field.placeholder || '',
            value: field.value || '',
            required: field.required || false,
            disabled: field.disabled || false,
            readonly: field.readOnly || false,
            maxLength: field.maxLength || 0,
            label: findFieldLabel(field, doc),
            selector: generateSelector(field, doc),
            boundingRect: field.getBoundingClientRect()
        };

        // Special handling for select elements
        if (field.tagName.toLowerCase() === 'select') {
            fieldInfo.options = Array.from(field.options).map(option => ({
                value: option.value,
                text: option.text,
                selected: option.selected
            }));
            fieldInfo.multiple = field.multiple;
        }

        // Special handling for input elements
        if (field.tagName.toLowerCase() === 'input') {
            fieldInfo.min = field.min || '';
            fieldInfo.max = field.max || '';
            fieldInfo.step = field.step || '';
            fieldInfo.pattern = field.pattern || '';
        }

        return fieldInfo;
    }

    function analyzeButton(button, index, doc) {
        return {
            index: index,
            tagName: button.tagName.toLowerCase(),
            type: button.type || '',
            text: button.textContent.trim() || button.value || '',
            name: button.name || '',
            id: button.id || '',
            disabled: button.disabled || false,
            formAction: button.formAction || '',
            formMethod: button.formMethod || '',
            selector: generateSelector(button, doc),
            boundingRect: button.getBoundingClientRect()
        };
    }

    function findFieldLabel(field, doc) {
        // Try to find associated label by 'for' attribute
        if (field.id) {
            const label = doc.querySelector(`label[for="${field.id}"]`);
            if (label) return label.textContent.trim();
        }

        // Try to find parent label
        let parent = field.parentElement;
        while (parent && parent !== doc.body) {
            if (parent.tagName.toLowerCase() === 'label') {
                return parent.textContent.trim();
            }
            parent = parent.parentElement;
        }

        // Try to find previous sibling label
        let sibling = field.previousElementSibling;
        while (sibling) {
            if (sibling.tagName.toLowerCase() === 'label') {
                return sibling.textContent.trim();
            }
            sibling = sibling.previousElementSibling;
        }

        // Try to find nearby text
        const nearbyText = findNearbyText(field, doc);
        return nearbyText || field.placeholder || field.name || '';
    }

    function findNearbyText(element, doc) {
        const rect = element.getBoundingClientRect();
        const textNodes = [];

        // Find text nodes near the element
        const walker = doc.createTreeWalker(
            doc.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            const text = node.textContent.trim();
            if (text.length > 2 && text.length < 50) {
                const range = doc.createRange();
                range.selectNode(node);
                const nodeRect = range.getBoundingClientRect();

                // Check if text is near the element
                const distance = Math.sqrt(
                    Math.pow(nodeRect.left - rect.left, 2) +
                    Math.pow(nodeRect.top - rect.top, 2)
                );

                if (distance < 100) {
                    textNodes.push({ text: text, distance: distance });
                }
            }
        }

        // Return the closest text
        textNodes.sort((a, b) => a.distance - b.distance);
        return textNodes.length > 0 ? textNodes[0].text : '';
    }

    function generateSelector(element, doc) {
        // Try ID first
        if (element.id) {
            return `#${element.id}`;
        }

        // Try name attribute
        if (element.name) {
            return `[name="${element.name}"]`;
        }

        // Try class-based selector
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }

        // Generate path-based selector
        const path = [];
        let current = element;

        while (current && current !== doc.body && path.length < 5) {
            let selector = current.tagName.toLowerCase();

            // Add index if there are siblings with same tag
            const siblings = Array.from(current.parentElement?.children || [])
                .filter(sibling => sibling.tagName === current.tagName);

            if (siblings.length > 1) {
                const index = siblings.indexOf(current) + 1;
                selector += `:nth-of-type(${index})`;
            }

            path.unshift(selector);
            current = current.parentElement;
        }

        return path.join(' > ');
    }















    // Alternative analysis method using content script injection
    function analyzePageWithContentScript() {
        // Get the current tab ID of the iframe
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                // Inject content script to analyze the page
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    function: analyzePageFromContentScript
                }, function(results) {
                    if (chrome.runtime.lastError) {
                        console.error('Content script injection failed:', chrome.runtime.lastError);
                        showNotification('فشل في تحليل الصفحة - جرب صفحة أخرى', 'error');
                        // Fallback to local file analysis
                        tryLocalFileAnalysis();
                    } else if (results && results[0] && results[0].result) {
                        const pageStructure = results[0].result;
                        displayPageAnalysis(pageStructure);
                        showNotification('تم تحليل هيكل الصفحة بنجاح', 'success');

                        // Store the analysis
                        chrome.storage.local.set({ pageStructure: pageStructure });
                    } else {
                        showNotification('لم يتم العثور على عناصر في الصفحة', 'warning');
                    }
                });
            } else {
                tryLocalFileAnalysis();
            }
        });
    }

    // Function to be injected into the page for analysis
    function analyzePageFromContentScript() {
        const pageStructure = {
            url: window.location.href,
            title: document.title,
            forms: [],
            buttons: [],
            inputs: [],
            selects: [],
            textareas: [],
            links: [],
            images: [],
            timestamp: Date.now()
        };

        // Analyze forms
        const forms = document.querySelectorAll('form');
        forms.forEach((form, index) => {
            const formInfo = {
                index: index,
                id: form.id || '',
                name: form.name || '',
                action: form.action || '',
                method: form.method || 'GET',
                fields: [],
                selector: generateSelectorForElement(form)
            };

            // Analyze form fields
            const fields = form.querySelectorAll('input, select, textarea');
            fields.forEach((field, fieldIndex) => {
                const fieldInfo = {
                    index: fieldIndex,
                    tagName: field.tagName.toLowerCase(),
                    type: field.type || '',
                    name: field.name || '',
                    id: field.id || '',
                    placeholder: field.placeholder || '',
                    value: field.value || '',
                    required: field.required || false,
                    disabled: field.disabled || false,
                    readonly: field.readOnly || false,
                    label: findLabelForField(field),
                    selector: generateSelectorForElement(field)
                };

                // Special handling for select elements
                if (field.tagName.toLowerCase() === 'select') {
                    fieldInfo.options = Array.from(field.options).map(option => ({
                        value: option.value,
                        text: option.text,
                        selected: option.selected
                    }));
                    fieldInfo.multiple = field.multiple;
                }

                formInfo.fields.push(fieldInfo);

                // Add to global arrays
                if (field.tagName.toLowerCase() === 'input') {
                    pageStructure.inputs.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'select') {
                    pageStructure.selects.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'textarea') {
                    pageStructure.textareas.push(fieldInfo);
                }
            });

            pageStructure.forms.push(formInfo);
        });

        // Analyze buttons
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], input[type="reset"]');
        buttons.forEach((button, index) => {
            const buttonInfo = {
                index: index,
                tagName: button.tagName.toLowerCase(),
                type: button.type || '',
                text: button.textContent.trim() || button.value || '',
                name: button.name || '',
                id: button.id || '',
                disabled: button.disabled || false,
                selector: generateSelectorForElement(button)
            };
            pageStructure.buttons.push(buttonInfo);
        });

        // Analyze links (limit to first 20)
        const links = document.querySelectorAll('a[href]');
        Array.from(links).slice(0, 20).forEach((link, index) => {
            pageStructure.links.push({
                index: index,
                text: link.textContent.trim(),
                href: link.href,
                target: link.target || '',
                selector: generateSelectorForElement(link)
            });
        });

        // Analyze images (limit to first 10)
        const images = document.querySelectorAll('img');
        Array.from(images).slice(0, 10).forEach((img, index) => {
            pageStructure.images.push({
                index: index,
                src: img.src,
                alt: img.alt || '',
                width: img.width || 0,
                height: img.height || 0,
                selector: generateSelectorForElement(img)
            });
        });

        // Helper functions for content script
        function generateSelectorForElement(element) {
            if (element.id) {
                return `#${element.id}`;
            }
            if (element.name) {
                return `[name="${element.name}"]`;
            }
            if (element.className) {
                const classes = element.className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    return `.${classes.join('.')}`;
                }
            }

            // Generate path-based selector
            const path = [];
            let current = element;
            while (current && current !== document.body && path.length < 4) {
                let selector = current.tagName.toLowerCase();
                const siblings = Array.from(current.parentElement?.children || [])
                    .filter(sibling => sibling.tagName === current.tagName);
                if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += `:nth-of-type(${index})`;
                }
                path.unshift(selector);
                current = current.parentElement;
            }
            return path.join(' > ');
        }

        function findLabelForField(field) {
            if (field.id) {
                const label = document.querySelector(`label[for="${field.id}"]`);
                if (label) return label.textContent.trim();
            }

            let parent = field.parentElement;
            while (parent && parent !== document.body) {
                if (parent.tagName.toLowerCase() === 'label') {
                    return parent.textContent.trim();
                }
                parent = parent.parentElement;
            }

            let sibling = field.previousElementSibling;
            while (sibling) {
                if (sibling.tagName.toLowerCase() === 'label') {
                    return sibling.textContent.trim();
                }
                sibling = sibling.previousElementSibling;
            }

            return field.placeholder || field.name || '';
        }

        return pageStructure;
    }

    // Fallback for local file analysis
    function tryLocalFileAnalysis() {
        showNotification('جاري المحاولة بطريقة محلية...', 'info');

        // Create a simple analysis for demonstration
        const demoStructure = {
            url: websiteFrame.src,
            title: 'تحليل تجريبي',
            forms: [{
                index: 0,
                id: 'demo-form',
                name: 'demoForm',
                action: '/submit',
                method: 'POST',
                fields: [
                    {
                        index: 0,
                        tagName: 'input',
                        type: 'text',
                        name: 'name',
                        label: 'الاسم',
                        required: true
                    },
                    {
                        index: 1,
                        tagName: 'input',
                        type: 'email',
                        name: 'email',
                        label: 'البريد الإلكتروني',
                        required: true
                    }
                ]
            }],
            buttons: [{
                index: 0,
                tagName: 'button',
                type: 'submit',
                text: 'إرسال'
            }],
            inputs: [],
            selects: [],
            textareas: [],
            links: [],
            images: [],
            timestamp: Date.now()
        };

        displayPageAnalysis(demoStructure);
        showNotification('تم إنشاء تحليل تجريبي - لاختبار كامل استخدم صفحة محلية', 'warning');
    }

    // Analyze local pages (works with file:// URLs)
    function analyzeLocalPage() {
        try {
            const iframeDoc = websiteFrame.contentDocument || websiteFrame.contentWindow.document;
            if (iframeDoc) {
                // Direct analysis for local files
                const pageStructure = analyzeDocumentStructure(iframeDoc);
                displayPageAnalysis(pageStructure);
                highlightDetectedElements(iframeDoc, pageStructure);
                showNotification('تم تحليل الصفحة المحلية بنجاح', 'success');

                // Store the analysis
                chrome.storage.local.set({ pageStructure: pageStructure });
            } else {
                showNotification('لا يمكن الوصول إلى الصفحة المحلية', 'error');
            }
        } catch (error) {
            console.error('Error analyzing local page:', error);
            showNotification('خطأ في تحليل الصفحة المحلية: ' + error.message, 'error');
        }
    }

    // Direct document analysis function
    function analyzeDocumentStructure(doc) {
        const pageStructure = {
            url: doc.location ? doc.location.href : 'local-file',
            title: doc.title || 'صفحة محلية',
            forms: [],
            buttons: [],
            inputs: [],
            selects: [],
            textareas: [],
            links: [],
            images: [],
            timestamp: Date.now()
        };

        // Analyze forms
        const forms = doc.querySelectorAll('form');
        forms.forEach((form, index) => {
            const formInfo = {
                index: index,
                id: form.id || '',
                name: form.name || '',
                action: form.action || '',
                method: form.method || 'GET',
                fields: [],
                selector: generateSelector(form, doc)
            };

            // Analyze form fields
            const fields = form.querySelectorAll('input, select, textarea');
            fields.forEach((field, fieldIndex) => {
                const fieldInfo = analyzeFormField(field, fieldIndex, doc);
                formInfo.fields.push(fieldInfo);

                // Add to global arrays
                if (field.tagName.toLowerCase() === 'input') {
                    pageStructure.inputs.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'select') {
                    pageStructure.selects.push(fieldInfo);
                } else if (field.tagName.toLowerCase() === 'textarea') {
                    pageStructure.textareas.push(fieldInfo);
                }
            });

            pageStructure.forms.push(formInfo);
        });

        // Analyze buttons
        const buttons = doc.querySelectorAll('button, input[type="button"], input[type="submit"], input[type="reset"]');
        buttons.forEach((button, index) => {
            const buttonInfo = analyzeButton(button, index, doc);
            pageStructure.buttons.push(buttonInfo);
        });

        // Analyze links (limit to first 20)
        const links = doc.querySelectorAll('a[href]');
        Array.from(links).slice(0, 20).forEach((link, index) => {
            pageStructure.links.push({
                index: index,
                text: link.textContent.trim(),
                href: link.href,
                target: link.target || '',
                selector: generateSelector(link, doc)
            });
        });

        // Analyze images (limit to first 10)
        const images = doc.querySelectorAll('img');
        Array.from(images).slice(0, 10).forEach((img, index) => {
            pageStructure.images.push({
                index: index,
                src: img.src,
                alt: img.alt || '',
                width: img.width || 0,
                height: img.height || 0,
                selector: generateSelector(img, doc)
            });
        });

        return pageStructure;
    }

    function displayPageAnalysis(structure) {
        // Create analysis panel
        let analysisPanel = document.getElementById('analysis-panel');
        if (!analysisPanel) {
            analysisPanel = document.createElement('div');
            analysisPanel.id = 'analysis-panel';
            analysisPanel.className = 'analysis-panel';
            analysisPanel.innerHTML = `
                <div class="analysis-header">
                    <h3>📊 تحليل هيكل الصفحة</h3>
                    <button class="btn btn-small" id="toggle-analysis">إخفاء/إظهار</button>
                </div>
                <div class="analysis-content" id="analysis-content"></div>
            `;

            // Insert after stats bar
            const statsBar = document.querySelector('.stats-bar');
            statsBar.parentNode.insertBefore(analysisPanel, statsBar.nextSibling);

            // Setup toggle functionality
            document.getElementById('toggle-analysis').addEventListener('click', function() {
                const content = document.getElementById('analysis-content');
                content.style.display = content.style.display === 'none' ? 'block' : 'none';
            });
        }

        const analysisContent = document.getElementById('analysis-content');
        analysisContent.innerHTML = `
            <div class="analysis-summary">
                <div class="summary-item">
                    <span class="summary-icon">📝</span>
                    <span class="summary-label">النماذج:</span>
                    <span class="summary-value">${structure.forms.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📋</span>
                    <span class="summary-label">حقول الإدخال:</span>
                    <span class="summary-value">${structure.inputs.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📋</span>
                    <span class="summary-label">القوائم المنسدلة:</span>
                    <span class="summary-value">${structure.selects.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">🔘</span>
                    <span class="summary-label">الأزرار:</span>
                    <span class="summary-value">${structure.buttons.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">🔗</span>
                    <span class="summary-label">الروابط:</span>
                    <span class="summary-value">${structure.links.length}</span>
                </div>
            </div>

            <div class="analysis-details">
                ${generateFormsAnalysis(structure.forms)}
                ${generateFieldsAnalysis(structure.inputs, 'حقول الإدخال', '📋')}
                ${generateFieldsAnalysis(structure.selects, 'القوائم المنسدلة', '📋')}
                ${generateButtonsAnalysis(structure.buttons)}
            </div>
        `;
    }

    function generateFormsAnalysis(forms) {
        if (forms.length === 0) return '';

        return `
            <div class="analysis-section">
                <h4>📝 النماذج المكتشفة (${forms.length})</h4>
                ${forms.map((form, index) => `
                    <div class="form-item">
                        <div class="form-header">
                            <strong>نموذج ${index + 1}</strong>
                            ${form.id ? `<span class="form-id">#${form.id}</span>` : ''}
                        </div>
                        <div class="form-details">
                            <div>الإجراء: ${form.action || 'غير محدد'}</div>
                            <div>الطريقة: ${form.method}</div>
                            <div>عدد الحقول: ${form.fields.length}</div>
                        </div>
                        <div class="form-fields">
                            ${form.fields.map(field => `
                                <div class="field-item">
                                    <span class="field-type">${field.type || field.tagName}</span>
                                    <span class="field-name">${field.label || field.name || field.placeholder || 'بدون اسم'}</span>
                                    ${field.required ? '<span class="field-required">مطلوب</span>' : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    function generateFieldsAnalysis(fields, title, icon) {
        if (fields.length === 0) return '';

        return `
            <div class="analysis-section">
                <h4>${icon} ${title} (${fields.length})</h4>
                <div class="fields-grid">
                    ${fields.map(field => `
                        <div class="field-card">
                            <div class="field-card-header">
                                <span class="field-type-badge">${field.type || field.tagName}</span>
                                ${field.required ? '<span class="required-badge">مطلوب</span>' : ''}
                            </div>
                            <div class="field-card-content">
                                <div class="field-label">${field.label || field.name || field.placeholder || 'بدون تسمية'}</div>
                                ${field.options ? `<div class="field-options">خيارات: ${field.options.length}</div>` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function generateButtonsAnalysis(buttons) {
        if (buttons.length === 0) return '';

        return `
            <div class="analysis-section">
                <h4>🔘 الأزرار المكتشفة (${buttons.length})</h4>
                <div class="buttons-grid">
                    ${buttons.map(button => `
                        <div class="button-card">
                            <div class="button-type">${button.type || button.tagName}</div>
                            <div class="button-text">${button.text || 'بدون نص'}</div>
                            ${button.disabled ? '<span class="disabled-badge">معطل</span>' : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function highlightDetectedElements(doc, structure) {
        try {
            // Remove existing highlights
            const existingHighlights = doc.querySelectorAll('.smart-highlight');
            existingHighlights.forEach(el => el.classList.remove('smart-highlight'));

            // Highlight forms
            structure.forms.forEach(form => {
                const formElement = doc.querySelector(form.selector);
                if (formElement) {
                    formElement.classList.add('smart-highlight', 'highlight-form');
                }
            });

            // Highlight input fields
            structure.inputs.forEach(input => {
                const inputElement = doc.querySelector(input.selector);
                if (inputElement) {
                    inputElement.classList.add('smart-highlight', 'highlight-input');
                }
            });

            // Highlight select fields
            structure.selects.forEach(select => {
                const selectElement = doc.querySelector(select.selector);
                if (selectElement) {
                    selectElement.classList.add('smart-highlight', 'highlight-select');
                }
            });

            // Highlight buttons
            structure.buttons.forEach(button => {
                const buttonElement = doc.querySelector(button.selector);
                if (buttonElement) {
                    buttonElement.classList.add('smart-highlight', 'highlight-button');
                }
            });

        } catch (error) {
            console.error('Error highlighting elements:', error);
        }
    }



    function mapColumnToField(column) {
        showNotification(`تم تعيين العمود: ${column}`, 'success');
        fieldMappings[column] = { mapped: true, timestamp: Date.now() };
    }

    function showCustomValueInput() {
        showNotification('ميزة القيمة المخصصة قيد التطوير', 'info');
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ القيمة', 'success');
        });
    }



    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideDown 0.3s ease-out;
            max-width: 400px;
            text-align: center;
            font-family: inherit;
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.background = '#28a745';
                break;
            case 'error':
                notification.style.background = '#dc3545';
                break;
            case 'warning':
                notification.style.background = '#ffc107';
                notification.style.color = '#212529';
                break;
            default:
                notification.style.background = '#17a2b8';
        }

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideUp 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }
});
