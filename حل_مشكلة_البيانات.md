# حل مشكلة عدم ظهور البيانات في split-view.html

## 🔍 تشخيص المشكلة

### الخطوة 1: التحقق من رفع البيانات
1. افتح النافذة المنبثقة للإضافة (انقر على أيقونة الإضافة)
2. تأكد من رفع ملف CSV بنجاح
3. يجب أن ترى رسالة "تم رفع الملف بنجاح"

### الخطوة 2: التحقق من حفظ البيانات
1. افتح Developer Tools (F12)
2. اذهب إلى Console
3. اكتب الأمر التالي:
```javascript
chrome.storage.local.get(['currentData'], (result) => {
    console.log('البيانات المحفوظة:', result);
});
```

### الخطوة 3: التحقق من تحميل البيانات في split-view
1. في صفحة split-view.html، افتح Console (F12)
2. ابحث عن رسائل مثل:
   - "📊 تحميل البيانات من التخزين..."
   - "✅ تم تحميل X صف و Y عمود"

## 🛠️ الحلول المقترحة

### الحل 1: إعادة رفع البيانات
1. اذهب إلى النافذة المنبثقة للإضافة
2. احذف الملف الحالي (إن وجد)
3. ارفع ملف `sample-data.csv` مرة أخرى
4. انتظر رسالة التأكيد
5. انقر على "Open Split View"

### الحل 2: تحديث البيانات يدوياً
1. في صفحة split-view.html
2. انقر على زر "🔄 تحديث البيانات" (في شريط الإحصائيات)
3. انتظر رسالة التأكيد

### الحل 3: إعادة تحميل الصفحة
1. في صفحة split-view.html
2. اضغط F5 أو Ctrl+R لإعادة التحميل
3. انتظر تحميل البيانات تلقائياً

### الحل 4: مسح التخزين وإعادة البدء
1. افتح Developer Tools (F12)
2. اذهب إلى Application > Storage
3. انقر على "Clear storage"
4. أعد رفع البيانات من البداية

## 🧪 اختبار النظام

### استخدام البيانات التجريبية
1. استخدم ملف `sample-data.csv` الموجود في المجلد
2. أو أنشئ ملف CSV بسيط:
```csv
الاسم,العمر,البريد
أحمد,25,<EMAIL>
فاطمة,30,<EMAIL>
محمد,28,<EMAIL>
```

### اختبار النموذج التجريبي
1. افتح `test-form.html` في متصفح منفصل
2. انسخ الرابط
3. الصقه في حقل URL في split-view.html
4. انقر "تحميل"

## 🔧 أوامر التشخيص المفيدة

### في Console الخاص بـ split-view.html:
```javascript
// التحقق من البيانات المحملة
console.log('البيانات:', splitView.data);
console.log('الأعمدة:', splitView.columns);

// إعادة تحميل البيانات
splitView.loadDataFromStorage();

// عرض البيانات يدوياً
splitView.displayData();
```

### في Console الخاص بالنافذة المنبثقة:
```javascript
// التحقق من البيانات الحالية
console.log('البيانات الحالية:', popup.currentData);

// إعادة فتح split-view
popup.openSplitView();
```

## 📋 قائمة التحقق

- [ ] تم رفع ملف CSV بنجاح
- [ ] ظهرت رسالة "تم رفع الملف بنجاح"
- [ ] تم فتح split-view في تبويب جديد
- [ ] لا توجد أخطاء في Console
- [ ] البيانات محفوظة في chrome.storage
- [ ] تم تحميل البيانات في split-view

## 🚨 مشاكل شائعة وحلولها

### المشكلة: "لا توجد بيانات"
**السبب:** لم يتم رفع الملف أو فشل في الحفظ
**الحل:** أعد رفع الملف وتأكد من رسالة التأكيد

### المشكلة: البيانات فارغة
**السبب:** ملف CSV فارغ أو تنسيق خاطئ
**الحل:** تأكد من أن الملف يحتوي على رؤوس أعمدة وبيانات

### المشكلة: خطأ في تحميل البيانات
**السبب:** مشكلة في الأذونات أو التخزين
**الحل:** أعد تحميل الإضافة وجرب مرة أخرى

## 📞 الدعم الفني

إذا استمرت المشكلة:
1. افتح Developer Tools
2. انسخ جميع الأخطاء من Console
3. تأكد من إصدار المتصفح (Chrome 88+ مطلوب)
4. جرب في نافذة تصفح خفي (Incognito)

---

**ملاحظة:** النظام المحسن يتضمن رسائل تشخيص مفصلة في Console لمساعدتك في تحديد المشكلة بدقة.
