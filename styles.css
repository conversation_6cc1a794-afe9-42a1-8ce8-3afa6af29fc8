/* Smart Form Filler Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container {
    width: 400px;
    min-height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* Header */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    font-size: 24px;
    margin-bottom: 5px;
    font-weight: 600;
}

.header p {
    font-size: 14px;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #4facfe;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #4facfe;
    background-color: #e3f2fd;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.upload-area p {
    margin: 5px 0;
    color: #666;
}

.file-types {
    font-size: 12px;
    color: #999;
}

/* File Info */
.file-info {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.file-name {
    font-weight: 600;
    color: #333;
}

.file-size {
    font-size: 12px;
    color: #666;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

/* Data Preview */
.data-preview {
    max-height: 200px;
    overflow: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
}

#data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

#data-table th,
#data-table td {
    padding: 8px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

#data-table th {
    background: #f8f9fa;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.data-stats {
    margin-top: 10px;
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #666;
}

/* Toggle Switch */
.mode-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4facfe;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.mode-label {
    font-weight: 600;
    color: #333;
}

.mode-description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Pattern Info */
.current-pattern {
    background: #f8f9ff;
    border: 1px solid #e3f2fd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.pattern-info {
    margin-bottom: 15px;
}

.pattern-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.pattern-url {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.pattern-fields {
    font-size: 12px;
    color: #4facfe;
}

.pattern-actions {
    display: flex;
    gap: 10px;
}

/* Patterns List */
.patterns-list {
    max-height: 150px;
    overflow-y: auto;
}

.no-patterns {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
}

/* Auto Fill Controls */
.autofill-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.row-selector,
.wait-time {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.row-selector label,
.wait-time label {
    font-weight: 500;
    color: #333;
}

.row-selector select,
.wait-time input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Progress Bar */
.autofill-progress {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #eee;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #666;
}

/* Footer */
.footer {
    background: #f8f9fa;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #eee;
}

.status {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
}

.version {
    font-size: 12px;
    color: #666;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Smart Detection Styles */
.detection-results {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.detected-elements {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.element-type {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.type-icon {
    font-size: 16px;
}

.type-name {
    flex: 1;
    font-weight: 500;
}

.type-count {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    min-width: 20px;
    text-align: center;
}

/* Recording Styles */
.recording-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recording-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.status-indicator {
    font-size: 18px;
    animation: pulse 2s infinite;
}

.status-indicator.recording {
    color: #dc3545;
}

.status-indicator.ready {
    color: #28a745;
}

.status-indicator.playing {
    color: #007bff;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-weight: 500;
    color: #495057;
}

.recording-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.recording-buttons .btn {
    flex: 1;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn-icon {
    font-size: 14px;
}

/* Recorded Steps */
.recorded-steps {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.steps-count {
    margin-bottom: 10px;
    font-weight: 500;
    color: #495057;
}

.steps-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.step-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.step-item:last-child {
    border-bottom: none;
}

.step-number {
    background: #007bff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    flex-shrink: 0;
}

.step-type {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.step-type.click {
    background: #e3f2fd;
    color: #1976d2;
}

.step-type.input {
    background: #e8f5e8;
    color: #388e3c;
}

.step-type.change {
    background: #fff3e0;
    color: #f57c00;
}

/* Recording Actions */
.recording-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.recording-actions .btn {
    flex: 1;
    min-width: 80px;
    font-size: 12px;
    padding: 8px 12px;
}

/* Saved Recordings */
.recordings-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.recording-item {
    padding: 12px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: background-color 0.2s;
}

.recording-item:hover {
    background-color: #f8f9fa;
}

.recording-item:last-child {
    border-bottom: none;
}

.recording-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recording-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.recording-date {
    font-size: 11px;
    color: #666;
}

.recording-info {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.recording-info span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.recording-actions-item {
    display: flex;
    gap: 6px;
    margin-top: 8px;
}

.recording-actions-item .btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: auto;
}

.btn-play {
    background: #28a745;
    color: white;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-export {
    background: #17a2b8;
    color: white;
}

.recordings-actions {
    display: flex;
    gap: 10px;
}

.recordings-actions .btn {
    flex: 1;
}

.no-recordings {
    padding: 30px 20px;
    text-align: center;
    color: #999;
    font-style: italic;
}

/* Recording Status Badge */
.recording-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.recording-status.active {
    background: #d4edda;
    color: #155724;
}

.recording-status.saved {
    background: #d1ecf1;
    color: #0c5460;
}
