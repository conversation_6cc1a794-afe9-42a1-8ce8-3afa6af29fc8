// Smart Form Filler - Popup Script
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const fileInput = document.getElementById('file-input');
    const uploadArea = document.getElementById('upload-area');
    const fileInfo = document.getElementById('file-info');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const removeFileBtn = document.getElementById('remove-file');
    const navigationSection = document.getElementById('navigation-section');
    const goToSplitViewBtn = document.getElementById('go-to-split-view');
    const statusElement = document.getElementById('status');

    let uploadedFileData = null;

    // File upload handling
    fileInput.addEventListener('change', handleFileSelect);
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleFileDrop);
    removeFileBtn.addEventListener('click', removeFile);
    goToSplitViewBtn.addEventListener('click', navigateToSplitView);

    // Drag and drop handlers
    function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    }

    function handleFileDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            processFile(files[0]);
        }
    }

    // File selection handler
    function handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            processFile(file);
        }
    }

    // Process uploaded file
    function processFile(file) {
        // Validate file type
        const allowedTypes = [
            'text/csv',
            'application/json',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        const fileExtension = file.name.split('.').pop().toLowerCase();
        const allowedExtensions = ['csv', 'json', 'xls', 'xlsx'];

        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
            showStatus('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو JSON أو Excel', 'error');
            return;
        }

        // Show file info
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        
        // Hide upload area and show file info
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'flex';

        // Read file content
        readFileContent(file);
    }

    // Read file content based on type
    function readFileContent(file) {
        const reader = new FileReader();
        const fileExtension = file.name.split('.').pop().toLowerCase();

        showStatus('جاري قراءة الملف...', 'info');

        reader.onload = function(e) {
            try {
                let data;
                
                if (fileExtension === 'csv') {
                    data = parseCSV(e.target.result);
                } else if (fileExtension === 'json') {
                    data = JSON.parse(e.target.result);
                } else if (fileExtension === 'xls' || fileExtension === 'xlsx') {
                    // For Excel files, we'll need to use a library like SheetJS
                    // For now, show a message that Excel support is coming
                    showStatus('دعم ملفات Excel قيد التطوير. يرجى استخدام CSV أو JSON', 'warning');
                    return;
                }

                if (data && data.length > 0) {
                    uploadedFileData = {
                        filename: file.name,
                        data: data,
                        columns: Object.keys(data[0] || {}),
                        rowCount: data.length
                    };

                    // Store data in chrome storage for split-view to access
                    chrome.storage.local.set({
                        uploadedData: uploadedFileData
                    }, function() {
                        showStatus('تم تحميل الملف بنجاح!', 'success');
                        showNavigationSection();
                    });
                } else {
                    showStatus('الملف فارغ أو تالف', 'error');
                }
            } catch (error) {
                console.error('Error reading file:', error);
                showStatus('خطأ في قراءة الملف', 'error');
            }
        };

        reader.onerror = function() {
            showStatus('خطأ في قراءة الملف', 'error');
        };

        if (fileExtension === 'json' || fileExtension === 'csv') {
            reader.readAsText(file);
        } else {
            reader.readAsArrayBuffer(file);
        }
    }

    // Parse CSV content
    function parseCSV(csvText) {
        const lines = csvText.trim().split('\n');
        if (lines.length < 2) return [];

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
            const row = {};
            
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            
            data.push(row);
        }

        return data;
    }

    // Show navigation section
    function showNavigationSection() {
        navigationSection.style.display = 'block';
        navigationSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Navigate to split view
    function navigateToSplitView() {
        // Open split-view.html in a new tab
        chrome.tabs.create({
            url: chrome.runtime.getURL('split-view.html')
        });
    }

    // Remove file
    function removeFile() {
        fileInput.value = '';
        uploadedFileData = null;
        
        // Reset UI
        uploadArea.style.display = 'block';
        fileInfo.style.display = 'none';
        navigationSection.style.display = 'none';
        
        // Clear stored data
        chrome.storage.local.remove('uploadedData');
        
        showStatus('تم إزالة الملف', 'info');
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Show status message
    function showStatus(message, type = 'info') {
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        
        // Auto-hide after 3 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                statusElement.textContent = 'جاهز';
                statusElement.className = 'status';
            }, 3000);
        }
    }

    // Initialize
    showStatus('جاهز');
});
