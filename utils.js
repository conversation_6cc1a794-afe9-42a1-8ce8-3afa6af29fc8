// Smart Form Filler - Utility Functions

// Common utility functions used across the extension

// Data validation utilities
const DataValidator = {
    // Validate CSV data structure
    validateCSV: function(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return { valid: false, error: 'البيانات فارغة أو غير صحيحة' };
        }

        // Check if all rows have the same structure
        const firstRowKeys = Object.keys(data[0]);
        for (let i = 1; i < data.length; i++) {
            const currentRowKeys = Object.keys(data[i]);
            if (currentRowKeys.length !== firstRowKeys.length) {
                return { valid: false, error: `الصف ${i + 1} له بنية مختلفة` };
            }
        }

        return { valid: true, columns: firstRowKeys, rows: data.length };
    },

    // Validate JSON data structure
    validateJSON: function(data) {
        try {
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }

            if (!Array.isArray(data)) {
                return { valid: false, error: 'البيانات يجب أن تكون مصفوفة من الكائنات' };
            }

            if (data.length === 0) {
                return { valid: false, error: 'البيانات فارغة' };
            }

            return { valid: true, columns: Object.keys(data[0]), rows: data.length };
        } catch (error) {
            return { valid: false, error: 'خطأ في تحليل JSON: ' + error.message };
        }
    }
};

// File handling utilities
const FileHandler = {
    // Get file extension
    getFileExtension: function(filename) {
        return filename.split('.').pop().toLowerCase();
    },

    // Check if file type is supported
    isSupportedFileType: function(filename) {
        const supportedExtensions = ['csv', 'json', 'xlsx', 'xls'];
        const extension = this.getFileExtension(filename);
        return supportedExtensions.includes(extension);
    },

    // Format file size for display
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Storage utilities
const StorageManager = {
    // Save data to chrome storage
    saveData: function(key, data, callback) {
        const storageData = {};
        storageData[key] = data;
        
        chrome.storage.local.set(storageData, function() {
            if (chrome.runtime.lastError) {
                console.error('Storage error:', chrome.runtime.lastError);
                if (callback) callback(false, chrome.runtime.lastError.message);
            } else {
                if (callback) callback(true);
            }
        });
    },

    // Get data from chrome storage
    getData: function(key, callback) {
        chrome.storage.local.get([key], function(result) {
            if (chrome.runtime.lastError) {
                console.error('Storage error:', chrome.runtime.lastError);
                if (callback) callback(null, chrome.runtime.lastError.message);
            } else {
                if (callback) callback(result[key] || null);
            }
        });
    },

    // Remove data from chrome storage
    removeData: function(key, callback) {
        chrome.storage.local.remove([key], function() {
            if (chrome.runtime.lastError) {
                console.error('Storage error:', chrome.runtime.lastError);
                if (callback) callback(false, chrome.runtime.lastError.message);
            } else {
                if (callback) callback(true);
            }
        });
    },

    // Clear all storage
    clearAll: function(callback) {
        chrome.storage.local.clear(function() {
            if (chrome.runtime.lastError) {
                console.error('Storage error:', chrome.runtime.lastError);
                if (callback) callback(false, chrome.runtime.lastError.message);
            } else {
                if (callback) callback(true);
            }
        });
    }
};

// UI utilities
const UIHelper = {
    // Show notification
    showNotification: function(message, type = 'info', duration = 3000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    },

    // Create loading spinner
    createLoadingSpinner: function(text = 'جاري التحميل...') {
        const spinner = document.createElement('div');
        spinner.className = 'loading';
        spinner.textContent = text;
        return spinner;
    },

    // Format date for display
    formatDate: function(date) {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
};

// CSV parsing utilities
const CSVParser = {
    // Parse CSV text to array of objects
    parse: function(csvText) {
        const lines = csvText.trim().split('\n');
        if (lines.length < 2) {
            throw new Error('ملف CSV يجب أن يحتوي على رأس وصف واحد على الأقل');
        }

        // Parse headers
        const headers = this.parseLine(lines[0]);
        const data = [];

        // Parse data rows
        for (let i = 1; i < lines.length; i++) {
            const values = this.parseLine(lines[i]);
            if (values.length === 0) continue; // Skip empty lines
            
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            
            data.push(row);
        }

        return data;
    },

    // Parse a single CSV line (handles quotes and commas)
    parseLine: function(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current.trim());
        return result;
    }
};

// Export utilities for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DataValidator,
        FileHandler,
        StorageManager,
        UIHelper,
        CSVParser
    };
}
