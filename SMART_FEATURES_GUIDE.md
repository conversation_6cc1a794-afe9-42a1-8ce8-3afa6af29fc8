# 🤖 دليل الميزات الذكية - Smart Form Filler

## نظرة عامة

تم تطوير نظام ذكي متقدم للتعرف التلقائي على عناصر الصفحة وتسجيل تسلسل العمليات. هذا الدليل يشرح كيفية استخدام الميزات الجديدة.

## 🎯 الميزات الجديدة

### 1. 🤖 التعرف التلقائي (Auto Detection)
- **التعرف على أزرار الإنشاء**: يكتشف تلقائياً الأزرار التي تحتوي على كلمات مثل "إنشاء"، "Create"، "جديد"، "New"
- **التعرف على القوائم المنسدلة**: يحلل القوائم العادية والمخصصة ويستخرج خياراتها
- **التعرف على أزرار الراديو**: يجمع أزرار الراديو في مجموعات ويحلل خياراتها
- **التعرف على النوافذ المنبثقة**: يكتشف النوافذ المنبثقة تلقائياً ويحلل محتواها

### 2. 📹 تسجيل الخطوات (Step Recording)
- **تسجيل النقرات**: يسجل جميع النقرات على الأزرار والعناصر التفاعلية
- **تسجيل الإدخال**: يسجل النصوص المدخلة في الحقول
- **تسجيل التغييرات**: يسجل التغييرات في القوائم المنسدلة وأزرار الراديو
- **إعادة التشغيل**: يعيد تشغيل التسلسل المسجل تلقائياً

### 3. 🪟 إدارة النوافذ المنبثقة (Modal Management)
- **كشف تلقائي**: يكتشف ظهور النوافذ المنبثقة
- **تحليل المحتوى**: يحلل الحقول والعناصر داخل النافذة
- **ربط البيانات**: يربط الحقول بأعمدة البيانات تلقائياً

## 🚀 كيفية الاستخدام

### الخطوة 1: تحضير البيانات
1. ارفع ملف CSV يحتوي على البيانات المطلوبة
2. تأكد من أن أسماء الأعمدة واضحة ومفهومة
3. استخدم ملف `smart-test-data.csv` للاختبار

### الخطوة 2: تفعيل التعرف التلقائي
1. افتح إضافة Smart Form Filler
2. انتقل إلى قسم "التعرف الذكي"
3. فعل مفتاح "التعرف التلقائي"
4. ستظهر العناصر المكتشفة مع تمييز ملون:
   - 🔴 أزرار الإنشاء (أحمر)
   - 🔵 القوائم المنسدلة (أزرق فاتح)
   - 🟣 أزرار الراديو (بنفسجي)
   - 🟠 النوافذ المنبثقة (برتقالي)

### الخطوة 3: تسجيل تسلسل العمليات
1. انتقل إلى قسم "تسجيل الخطوات"
2. اضغط "🔴 بدء التسجيل"
3. قم بالعمليات المطلوبة:
   - انقر على زر الإنشاء
   - املأ النموذج في النافذة المنبثقة
   - اختر من القوائم المنسدلة
   - حدد أزرار الراديو
   - اضغط زر الحفظ
4. اضغط "⏹️ إيقاف التسجيل"

### الخطوة 4: إعادة التشغيل التلقائي
1. تأكد من وجود بيانات محملة
2. اضغط "▶️ تشغيل التسجيل"
3. ستتم إعادة تشغيل جميع الخطوات تلقائياً
4. سيتم ملء النموذج بالبيانات من الملف

## 🔧 الإعدادات المتقدمة

### تخصيص التعرف التلقائي
يمكن تخصيص كلمات البحث للتعرف على الأزرار:
- أزرار الإنشاء: "إنشاء"، "Create"، "جديد"، "New"، "إضافة"، "Add"
- أزرار الإرسال: "حفظ"، "Save"، "إرسال"، "Submit"

### تخصيص التسجيل
- **مدة الانتظار**: يمكن تعديل الوقت بين الخطوات
- **فلترة الأحداث**: يمكن تجاهل أحداث معينة
- **حفظ التسجيلات**: يتم حفظ التسجيلات تلقائياً

## 📋 ملفات الاختبار

### test-smart-features.html
صفحة اختبار شاملة تحتوي على:
- أزرار إنشاء متنوعة
- قوائم منسدلة عادية ومخصصة
- أزرار راديو مجمعة
- نافذة منبثقة مع نموذج كامل

### smart-test-data.csv
ملف بيانات اختبار يحتوي على:
- 20 منتج متنوع
- أعمدة: name, sku, price, category, unit, type, description
- بيانات باللغة العربية

## 🎨 التمييز البصري

### ألوان التعرف التلقائي
- **🔴 أحمر**: أزرار الإنشاء والإضافة
- **🔵 أزرق فاتح**: القوائم المنسدلة
- **🟣 بنفسجي**: أزرار الراديو
- **🟠 برتقالي**: النوافذ المنبثقة

### حالات التسجيل
- **🔴 أحمر نابض**: جاري التسجيل
- **🟢 أخضر**: جاهز للتسجيل
- **▶️ أزرق**: جاري التشغيل

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### لا يتم اكتشاف الأزرار
- تأكد من تفعيل وضع التعرف التلقائي
- تحقق من أن الأزرار تحتوي على النصوص المطلوبة
- جرب إعادة تحميل الصفحة

#### لا يتم تسجيل الخطوات
- تأكد من بدء التسجيل قبل القيام بالعمليات
- تحقق من أن العناصر مرئية وقابلة للنقر
- تجنب النقر السريع المتتالي

#### فشل إعادة التشغيل
- تأكد من وجود بيانات محملة
- تحقق من أن العناصر ما زالت موجودة في الصفحة
- جرب تسجيل تسلسل جديد

### رسائل الخطأ الشائعة
- "لا توجد خطوات مسجلة للتشغيل": ابدأ تسجيل جديد
- "لا توجد بيانات للتشغيل": ارفع ملف البيانات
- "لم يتم العثور على العنصر": العنصر غير موجود أو تغير

## 🚀 نصائح للاستخدام الأمثل

### أفضل الممارسات
1. **ابدأ بالتعرف التلقائي**: فعل هذا الوضع أولاً لرؤية العناصر المكتشفة
2. **سجل خطوات بسيطة**: ابدأ بتسلسل بسيط ثم أضف التعقيد
3. **اختبر التسجيل**: جرب إعادة التشغيل قبل الاستخدام الفعلي
4. **استخدم بيانات واضحة**: تأكد من أن أسماء الأعمدة مفهومة

### تحسين الأداء
- استخدم محددات فريدة للعناصر (ID, Name)
- تجنب العناصر المتحركة أو المتغيرة
- انتظر تحميل الصفحة بالكامل قبل البدء

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات:
1. تحقق من هذا الدليل أولاً
2. جرب ملفات الاختبار المرفقة
3. تأكد من تحديث الإضافة لآخر إصدار

## 🔄 التحديثات المستقبلية

### ميزات قادمة
- **التعرف على الجداول**: كشف وتحليل الجداول تلقائياً
- **التعرف على النماذج المعقدة**: دعم النماذج متعددة الخطوات
- **الذكاء الاصطناعي**: استخدام AI لتحسين التعرف
- **التكامل مع APIs**: ربط مباشر مع قواعد البيانات

---

**ملاحظة**: هذه الميزات في مرحلة التطوير المستمر. نرحب بملاحظاتكم واقتراحاتكم لتحسين الأداء.
