<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة اختبار تحليل الهيكل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .form-section h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .required {
            color: red;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .links-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .links-section a {
            display: inline-block;
            margin: 5px 10px;
            color: #007bff;
            text-decoration: none;
        }
        
        .links-section a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 صفحة اختبار تحليل هيكل الصفحة</h1>
        
        <div class="info-box">
            <strong>📋 تعليمات الاختبار:</strong><br>
            1. افتح هذه الصفحة في iframe داخل split-view.html<br>
            2. انقر على زر "🔍 كشف الحقول" لتحليل هيكل الصفحة<br>
            3. راقب تمييز العناصر المختلفة بألوان مختلفة<br>
            4. تحقق من ظهور تحليل مفصل في اللوحة الجانبية
        </div>

        <!-- نموذج معلومات شخصية -->
        <div class="form-section">
            <h2>📝 نموذج المعلومات الشخصية</h2>
            <form id="personal-info-form" action="/submit-personal" method="POST">
                <div class="form-group">
                    <label for="full-name">الاسم الكامل <span class="required">*</span></label>
                    <input type="text" id="full-name" name="fullName" placeholder="أدخل اسمك الكامل" required>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني <span class="required">*</span></label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" name="phone" placeholder="+966 50 123 4567">
                </div>
                
                <div class="form-group">
                    <label for="age">العمر</label>
                    <input type="number" id="age" name="age" min="18" max="100" placeholder="25">
                </div>
                
                <div class="form-group">
                    <label for="gender">الجنس</label>
                    <select id="gender" name="gender">
                        <option value="">اختر الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="city">المدينة</label>
                    <select id="city" name="city">
                        <option value="">اختر المدينة</option>
                        <option value="riyadh">الرياض</option>
                        <option value="jeddah">جدة</option>
                        <option value="dammam">الدمام</option>
                        <option value="mecca">مكة المكرمة</option>
                        <option value="medina">المدينة المنورة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="bio">نبذة شخصية</label>
                    <textarea id="bio" name="bio" rows="4" placeholder="اكتب نبذة مختصرة عن نفسك..."></textarea>
                </div>
                
                <button type="submit" class="btn-success">💾 حفظ المعلومات</button>
                <button type="reset" class="btn-warning">🔄 إعادة تعيين</button>
            </form>
        </div>

        <!-- نموذج تسجيل الدخول -->
        <div class="form-section">
            <h2>🔐 نموذج تسجيل الدخول</h2>
            <form id="login-form" action="/login" method="POST">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" placeholder="اسم المستخدم" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" placeholder="كلمة المرور" required>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="remember" value="1"> تذكرني
                    </label>
                </div>
                
                <button type="submit" class="btn-success">🚀 تسجيل الدخول</button>
                <button type="button" class="btn-warning">❓ نسيت كلمة المرور؟</button>
            </form>
        </div>

        <!-- أزرار إضافية للاختبار -->
        <div class="form-section">
            <h2>🔘 أزرار متنوعة للاختبار</h2>
            <button type="button" onclick="alert('تم النقر!')">زر عادي</button>
            <button type="button" class="btn-success">زر نجاح</button>
            <button type="button" class="btn-warning">زر تحذير</button>
            <button type="button" class="btn-danger">زر خطر</button>
            <button type="button" disabled>زر معطل</button>
            
            <input type="button" value="زر إدخال" onclick="alert('زر إدخال!')">
            <input type="submit" value="زر إرسال" form="test-form">
            <input type="reset" value="زر إعادة تعيين" form="test-form">
        </div>

        <!-- روابط للاختبار -->
        <div class="links-section">
            <h3>🔗 روابط للاختبار</h3>
            <a href="#home">الرئيسية</a>
            <a href="#about">حول</a>
            <a href="#contact">اتصل بنا</a>
            <a href="#services">الخدمات</a>
            <a href="https://example.com" target="_blank">رابط خارجي</a>
            <a href="mailto:<EMAIL>">البريد الإلكتروني</a>
            <a href="tel:+966501234567">الهاتف</a>
        </div>

        <!-- صور للاختبار -->
        <div class="form-section">
            <h3>🖼️ صور للاختبار</h3>
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPgogIDx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+U2FtcGxlPC90ZXh0Pgo8L3N2Zz4K" alt="صورة تجريبية 1" width="100" height="100">
            
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzI4YTc0NSIvPgogIDx0ZXh0IHg9Ijc1IiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+SW1hZ2UgMjwvdGV4dD4KICA8L3N2Zz4K" alt="صورة تجريبية 2" width="150" height="100">
        </div>
    </div>

    <script>
        // إضافة بعض التفاعل للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل صفحة الاختبار');
            
            // إضافة مستمعي الأحداث للنماذج
            document.getElementById('personal-info-form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('تم إرسال نموذج المعلومات الشخصية!');
            });
            
            document.getElementById('login-form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('تم إرسال نموذج تسجيل الدخول!');
            });
        });
    </script>
</body>
</html>
